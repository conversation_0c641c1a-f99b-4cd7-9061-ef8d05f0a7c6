@echo off
REM Skrypt uruchamiający TridentOS na Windows
REM Automatycznie wykrywa platformę i stosuje odpowiednie optymalizacje

echo ================================================
echo TridentOS - Windows Launcher
echo ================================================

REM Sprawdź czy Python jest dostępny
python --version >nul 2>&1
if errorlevel 1 (
    echo BŁĄD: Python nie jest zainstalowany lub niedostępny w PATH
    echo Zainstaluj Python 3.8+ i spróbuj ponownie
    pause
    exit /b 1
)

REM Sprawdź czy psutil jest zainstalowany
python -c "import psutil" >nul 2>&1
if errorlevel 1 (
    echo Instalowanie brakujących zależności...
    pip install psutil
    if errorlevel 1 (
        echo BŁĄD: Nie można zainstalować psutil
        pause
        exit /b 1
    )
)

REM Przejdź do katalogu aplikacji
cd /d "%~dp0venv\TridentUI2"

REM Sprawdź czy pliki aplikacji istnieją
if not exist "main.py" (
    echo BŁĄD: Nie znaleziono pliku main.py
    echo Sprawdź czy jesteś w poprawnym katalogu
    pause
    exit /b 1
)

echo Uruchamianie TridentOS...
echo.

REM Uruchom aplikację z optymalizacjami
if exist "start_rpi5.py" (
    echo Używanie zoptymalizowanego launchera...
    python start_rpi5.py
) else (
    echo Używanie standardowego uruchomienia...
    python main.py
)

if errorlevel 1 (
    echo.
    echo BŁĄD: Aplikacja zakończona z błędem
    echo Sprawdź logi powyżej
    pause
)

echo.
echo Aplikacja zakończona
pause
