"""
Konfiguracja zoptymalizowana dla Raspberry Pi 5
Zawiera ustawienia wydajności, pamięci i zasobów dostosowane do RPi5
"""

import os
import platform
import psutil

# Wykrywanie platformy
IS_RASPBERRY_PI = platform.machine().startswith('arm') or 'raspberry' in platform.node().lower()

# Konfiguracja podstawowa
class RPi5Config:
    """Klasa konfiguracji dla Raspberry Pi 5"""
    
    def __init__(self):
        self.is_rpi = IS_RASPBERRY_PI
        self.cpu_count = os.cpu_count() or 4
        self.memory_total = self._get_memory_total()
        
    def _get_memory_total(self):
        """Pobiera całkowitą ilość pamięci RAM"""
        try:
            return psutil.virtual_memory().total // (1024 * 1024)  # MB
        except:
            return 8192  # Domyślnie 8GB dla RPi5
    
    @property
    def graphics_config(self):
        """Konfiguracja grafiki dla RPi5"""
        if self.is_rpi:
            return {
                'width': 1280,
                'height': 720,
                'maxfps': 25,
                'vsync': 1,
                'multisamples': 0,
                'texture_limit': 512,
                'retain_time': 5,
                'gl_backend': 'gl',
                'window': 'sdl2',
                'fullscreen': 'auto',
                'borderless': True,
                'resizable': False
            }
        else:
            return {
                'width': 1280,
                'height': 720,
                'maxfps': 30,
                'vsync': 0,
                'multisamples': 0,
                'texture_limit': 1024,
                'retain_time': 10,
                'gl_backend': 'gl',
                'window': 'sdl2',
                'fullscreen': False,
                'borderless': False,
                'resizable': False
            }
    
    @property
    def memory_config(self):
        """Konfiguracja pamięci dla RPi5"""
        if self.is_rpi:
            return {
                'cache_image_limit': 100,
                'cache_texture_limit': 300,
                'cache_shader_limit': 50,
                'cache_atlas_limit': 20,
                'cache_data_limit': 50,
                'cache_timeout_image': 30,
                'cache_timeout_texture': 60,
                'cache_timeout_shader': 120,
                'gc_threshold': (700, 10, 10),
                'gc_interval': 30.0
            }
        else:
            return {
                'cache_image_limit': 200,
                'cache_texture_limit': 1000,
                'cache_shader_limit': 1000,
                'cache_atlas_limit': 50,
                'cache_data_limit': 100,
                'cache_timeout_image': 60,
                'cache_timeout_texture': 120,
                'cache_timeout_shader': 300,
                'gc_threshold': (700, 10, 10),
                'gc_interval': 60.0
            }
    
    @property
    def performance_config(self):
        """Konfiguracja wydajności dla RPi5"""
        if self.is_rpi:
            return {
                'ui_update_interval': 0.2,
                'state_update_interval': 1.0,
                'alarm_check_interval': 2.0,
                'memory_cleanup_interval': 30.0,
                'max_active_alarms': 50,
                'max_history_alarms': 100,
                'batch_size': 5,
                'io_cache_ttl': 0.5,
                'io_write_interval': 1.0,
                'enable_profiling': False
            }
        else:
            return {
                'ui_update_interval': 0.1,
                'state_update_interval': 0.5,
                'alarm_check_interval': 1.0,
                'memory_cleanup_interval': 60.0,
                'max_active_alarms': 100,
                'max_history_alarms': 500,
                'batch_size': 10,
                'io_cache_ttl': 0.2,
                'io_write_interval': 0.5,
                'enable_profiling': True
            }
    
    @property
    def system_config(self):
        """Konfiguracja systemowa dla RPi5"""
        if self.is_rpi:
            return {
                'enable_gpu_acceleration': True,
                'use_hardware_decoding': True,
                'cpu_affinity': [2, 3],  # Użyj rdzeni 2-3 dla aplikacji
                'nice_priority': -5,     # Wyższy priorytet
                'enable_swap': False,    # Wyłącz swap dla lepszej wydajności
                'temp_threshold': 70,    # Próg temperatury CPU
                'throttle_temp': 75,     # Temperatura throttlingu
                'enable_watchdog': True,
                'log_level': 'ERROR'
            }
        else:
            return {
                'enable_gpu_acceleration': True,
                'use_hardware_decoding': False,
                'cpu_affinity': None,
                'nice_priority': 0,
                'enable_swap': True,
                'temp_threshold': 80,
                'throttle_temp': 85,
                'enable_watchdog': False,
                'log_level': 'WARNING'
            }
    
    def apply_system_optimizations(self):
        """Stosuje optymalizacje systemowe"""
        if not self.is_rpi:
            return
        
        try:
            # Ustawienie priorytetu procesu
            import psutil
            p = psutil.Process()
            p.nice(self.system_config['nice_priority'])
            
            # Ustawienie CPU affinity
            if self.system_config['cpu_affinity']:
                p.cpu_affinity(self.system_config['cpu_affinity'])
            
            # Zmienne środowiskowe dla GPU
            os.environ['KIVY_GL_BACKEND'] = self.graphics_config['gl_backend']
            os.environ['KIVY_WINDOW'] = self.graphics_config['window']
            os.environ['KIVY_METRICS_DENSITY'] = '1'
            os.environ['KIVY_METRICS_FONTSCALE'] = '1'
            
            # Optymalizacje dla VideoCore VII
            os.environ['VC_CMA_SIZE'] = '256'  # 256MB dla GPU
            os.environ['GPU_MEM'] = '256'
            
        except Exception as e:
            print(f"Błąd podczas stosowania optymalizacji: {e}")
    
    def get_thermal_status(self):
        """Sprawdza status termiczny RPi5"""
        if not self.is_rpi:
            return {'temp': 0, 'throttled': False}
        
        try:
            # Odczyt temperatury CPU
            with open('/sys/class/thermal/thermal_zone0/temp', 'r') as f:
                temp = int(f.read().strip()) / 1000.0
            
            # Sprawdzenie throttlingu
            throttled = temp > self.system_config['throttle_temp']
            
            return {
                'temp': temp,
                'throttled': throttled,
                'warning': temp > self.system_config['temp_threshold']
            }
        except:
            return {'temp': 0, 'throttled': False, 'warning': False}
    
    def get_memory_usage(self):
        """Zwraca informacje o użyciu pamięci"""
        try:
            mem = psutil.virtual_memory()
            return {
                'total': mem.total // (1024 * 1024),
                'available': mem.available // (1024 * 1024),
                'percent': mem.percent,
                'used': mem.used // (1024 * 1024)
            }
        except:
            return {'total': 8192, 'available': 4096, 'percent': 50, 'used': 4096}

# Globalna instancja konfiguracji
config = RPi5Config()

# Funkcje pomocnicze
def is_raspberry_pi():
    """Sprawdza czy aplikacja działa na Raspberry Pi"""
    return config.is_rpi

def get_optimal_thread_count():
    """Zwraca optymalną liczbę wątków dla platformy"""
    if config.is_rpi:
        return min(2, config.cpu_count - 1)  # Zostaw jeden rdzeń dla systemu
    else:
        return config.cpu_count

def should_enable_feature(feature_name):
    """Sprawdza czy dana funkcja powinna być włączona na danej platformie"""
    rpi_disabled_features = [
        'advanced_animations',
        'high_quality_textures',
        'complex_shaders',
        'detailed_logging',
        'debug_mode'
    ]
    
    if config.is_rpi and feature_name in rpi_disabled_features:
        return False
    return True

def get_cache_size(cache_type):
    """Zwraca optymalny rozmiar cache dla danego typu"""
    cache_sizes = config.memory_config
    return cache_sizes.get(f'cache_{cache_type}_limit', 100)

def get_update_interval(interval_type):
    """Zwraca optymalny interwał aktualizacji dla danego typu"""
    intervals = config.performance_config
    return intervals.get(f'{interval_type}_interval', 1.0)
