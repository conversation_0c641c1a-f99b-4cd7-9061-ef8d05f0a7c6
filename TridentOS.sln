﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.10.34916.146
MinimumVisualStudioVersion = 10.0.40219.1
Project("{888888A0-9F3D-457C-B088-3A5042F75D52}") = "TridentOS", "TridentOS\TridentOS.pyproj", "{FEAA4EC5-B9DF-4E48-8B78-76DFE2962EE8}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{FEAA4EC5-B9DF-4E48-8B78-76DFE2962EE8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FEAA4EC5-B9DF-4E48-8B78-76DFE2962EE8}.Release|Any CPU.ActiveCfg = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {E6A5B242-A2B7-4756-B874-7C13FB1672A7}
	EndGlobalSection
EndGlobal
