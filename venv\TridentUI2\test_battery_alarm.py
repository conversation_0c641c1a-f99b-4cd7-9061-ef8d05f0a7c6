#!/usr/bin/env python3
"""
Skrypt testowy do demonstracji funkcjonalności alarmu niskiego poziomu baterii.
Zoptymalizowany dla Raspberry Pi 5:
- Zredukowane operacje I/O
- Minimalne użycie pamięci
- Efektywne zarządzanie zasobami
"""
import state_manager
from contextlib import contextmanager
from typing import Dict, Any, Generator

@contextmanager
def state_context() -> Generator[Dict[str, Any], None, None]:
    """
    Kontekstowy menedżer do bezpiecznej manipulacji stanem.
    Redukuje ilość operacji I/O i zapewnia atomowość operacji.
    """
    state = state_manager.load_state()
    try:
        yield state
    finally:
        state_manager.save_state(state)

def test_battery_alarm() -> None:
    """
    Testuje alarm niskiego poziomu baterii z minimalnym wpływem na zasoby systemu.
    Używa buforowania stanu i zoptymalizowanych operacji I/O.
    """
    print("\n=== ROZPOCZĘCIE TESTU ALARMU NISKIEGO POZIOMU BATERII ===")
    
    with state_context() as state:
        original_battery = state.get("battery_level", 80)
        print(f"Oryginalny poziom baterii: {original_battery}%")
        
        # Zmiana poziomu baterii (jednorazowa operacja zapisu)
        state["battery_level"] = 15
        print("Ustawiono niski poziom baterii: 15%")
    
    print("Uruchom aplikację główną, aby zobaczyć alarm.")
    input("Naciśnij Enter, aby przywrócić oryginalny poziom baterii...\n")
    
    with state_context() as state:
        state["battery_level"] = original_battery
        print(f"Przywrócono oryginalny poziom baterii: {original_battery}%")
    
    print("Uruchom aplikację główną, aby zobaczyć, że alarm został usunięty.")
    print("=== ZAKOŃCZENIE TESTU ALARMU NISKIEGO POZIOMU BATERII ===\n")

if __name__ == "__main__":
    try:
        test_battery_alarm()
    except KeyboardInterrupt:
        print("\nTest przerwany przez użytkownika")
    except Exception as e:
        print(f"\nBłąd podczas testu: {e}")
