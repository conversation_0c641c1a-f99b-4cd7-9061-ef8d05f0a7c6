# Optymalizacja dla Raspberry Pi 5
import platform
import os

# Wykrywanie czy działa na Raspberry Pi
IS_RASPBERRY_PI = platform.machine().startswith('arm') or 'raspberry' in platform.node().lower()

# Konfiguracja grafiki zoptymalizowana dla RPi5
if IS_RASPBERRY_PI:
    # Raspberry Pi 5 - wykorzystanie GPU VideoCore VII
    Config.set('graphics', 'width', '1280')
    Config.set('graphics', 'height', '720')
    Config.set('graphics', 'resizable', '0')
    Config.set('graphics', 'maxfps', '25')  # Niższe FPS dla RPi5
    Config.set('graphics', 'vsync', '1')    # Włącz vsync na RPi5 dla płynności
    Config.set('graphics', 'multisamples', '0')  # Wyłącz antyaliasing
    Config.set('graphics', 'show_cursor', '1')
    Config.set('graphics', 'kivy_clock', 'interrupt')  # Lepszy dla ARM
    Config.set('graphics', 'window_state', 'maximized')

    # Optymalizacje pamięci dla RPi5
    Config.set('graphics', 'texture_limit', '512')  # Limit tekstur
    Config.set('graphics', 'retain_time', '5')      # Krótszy czas przechowywania

    # Optymalizacje OpenGL ES dla RPi5
    os.environ['KIVY_GL_BACKEND'] = 'gl'
    os.environ['KIVY_WINDOW'] = 'sdl2'

    # Optymalizacje systemowe dla RPi5
    os.environ['KIVY_METRICS_DENSITY'] = '1'
    os.environ['KIVY_METRICS_FONTSCALE'] = '1'

else:
    # Konfiguracja dla PC/innych platform
    Config.set('graphics', 'width', '1280')
    Config.set('graphics', 'height', '720')
    Config.set('graphics', 'resizable', '0')
    Config.set('graphics', 'maxfps', '30')
    Config.set('graphics', 'kivy_clock', 'free_all')
    Config.set('graphics', 'vsync', '0')
    Config.set('graphics', 'multisamples', '0')
    Config.set('graphics', 'show_cursor', '1')

from kivy.app import App
from kivy.lang import Builder
from kivy.uix.screenmanager import ScreenManager, Screen, NoTransition
from kivy.uix.behaviors import ButtonBehavior
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.label import Label
from kivy.uix.widget import Widget
from kivy.clock import Clock
from datetime import datetime
from kivy.properties import NumericProperty, BooleanProperty, StringProperty, ObjectProperty, ListProperty, DictProperty
from kivy.cache import Cache
import os
import json
import gc
import weakref
import time

# Konfiguracja pamięci podręcznej Kivy - zoptymalizowana dla RPi5
if IS_RASPBERRY_PI:
    # Mniejsze limity pamięci podręcznej dla RPi5 (8GB RAM)
    Cache.register('kv.image', limit=100, timeout=30)      # Zmniejszone z 200
    Cache.register('kv.texture', limit=300, timeout=60)    # Zmniejszone z 1000
    Cache.register('kv.shader', limit=50, timeout=120)     # Zmniejszone z 1000
    Cache.register('kv.atlas', limit=20, timeout=300)      # Nowy cache dla atlasów
    Cache.register('app.data', limit=50, timeout=600)      # Cache dla danych aplikacji
else:
    # Większe limity dla PC
    Cache.register('kv.image', limit=200, timeout=60)
    Cache.register('kv.texture', limit=1000, timeout=120)
    Cache.register('kv.shader', limit=1000, timeout=300)
    Cache.register('kv.atlas', limit=50, timeout=600)
    Cache.register('app.data', limit=100, timeout=1200)

# Statystyki wydajności
_performance_stats = {
    'ui_updates': 0,
    'last_gc_time': time.time(),
    'gc_interval': 60.0,  # Interwał czyszczenia pamięci w sekundach
    'last_state_update': 0,
    'state_update_count': 0
}

# Import modułów do obsługi pliku system_state.json i alarmów
import state_manager
import alarm_manager
import alarm_monitor

# Zoptymalizowana klasa do autonomicznego wyświetlania zegara
class ClockWidget(Label):
    """
    Widget zegara, który autonomicznie aktualizuje swój czas.
    Zoptymalizowany o redukcję niepotrzebnych aktualizacji UI i zarządzanie zasobami.
    """
    def __init__(self, **kwargs):
        super(ClockWidget, self).__init__(**kwargs)
        # Przechowywanie ostatniego wyświetlonego czasu
        self._last_time = ""
        # Flaga aktywności widgetu
        self._active = True
        # Natychmiastowa aktualizacja czasu
        self.update_time(None)
        # Uruchomienie aktualizacji co 1 sekundę z opóźnieniem
        self.clock_event = Clock.schedule_interval(self.update_time, 1)

    def update_time(self, dt):
        """
        Aktualizuje wyświetlany czas tylko jeśli się zmienił i widget jest aktywny.
        Zoptymalizowana wersja z redukcją niepotrzebnych aktualizacji UI.
        """
        # Sprawdzenie, czy widget jest aktywny
        if not self._active:
            return

        # Pobranie aktualnego czasu z pamięcią podręczną
        current_time = state_manager.get_current_time()

        # Aktualizuj tekst tylko jeśli czas się zmienił
        if current_time != self._last_time:
            self.text = current_time
            self._last_time = current_time

            # Wymuszenie odświeżenia tylko tego widgetu
            self.canvas.ask_update()

    def on_parent(self, widget, parent):
        """
        Zatrzymuje aktualizację zegara, gdy widget jest usuwany z drzewa widgetów.
        Zarządza zasobami, aby uniknąć wycieków pamięci.
        """
        if parent is None:
            # Oznaczenie widgetu jako nieaktywny
            self._active = False
            # Zatrzymanie aktualizacji
            if self.clock_event:
                self.clock_event.cancel()
                self.clock_event = None

# Rejestracja ClockWidget w Factory, aby można było go używać w plikach kv
from kivy.factory import Factory
Factory.register('ClockWidget', ClockWidget)

# Globalne zmienne do śledzenia wydajności - zoptymalizowane dla RPi5
_last_ui_update = 0
_performance_mode = True   # Tryb wydajności włączony domyślnie

# Interwały aktualizacji dostosowane do platformy
if IS_RASPBERRY_PI:
    _ui_update_interval = 0.2      # Rzadsze aktualizacje UI na RPi5 (200ms)
    _state_update_interval = 1.0   # Aktualizacja stanu co sekundę
    _alarm_check_interval = 2.0    # Sprawdzanie alarmów co 2 sekundy
    _memory_cleanup_interval = 30.0 # Czyszczenie pamięci co 30 sekund
else:
    _ui_update_interval = 0.1      # Częstsze aktualizacje na PC (100ms)
    _state_update_interval = 0.5   # Aktualizacja stanu co 500ms
    _alarm_check_interval = 1.0    # Sprawdzanie alarmów co sekundę
    _memory_cleanup_interval = 60.0 # Czyszczenie pamięci co minutę

# Liczniki wydajności
_performance_stats = {
    'ui_updates': 0,
    'state_updates': 0,
    'memory_cleanups': 0,
    'last_cleanup': 0,
    'last_gc_time': 0,
    'gc_interval': _memory_cleanup_interval
}

Builder.load_file("ui/home.kv")
Builder.load_file("ui/climate.kv")
Builder.load_file("ui/lightning.kv")
Builder.load_file("ui/battery.kv")
Builder.load_file("ui/alarm.kv")
Builder.load_file("ui/engine.kv")
Builder.load_file("ui/water.kv")
Builder.load_file("ui/fuel.kv")
Builder.load_file("ui/autopilot.kv")

class Tile(Screen):
    title = StringProperty()
    value = StringProperty()
    icon = StringProperty()

class LightningTile(ButtonBehavior, BoxLayout):
    def on_release(self):
        App.get_running_app().root.current = "lightning"

    def update_status(self, active):
        # Aktualizacja statusu oświetlenia w kafelku
        for child in self.walk():
            if isinstance(child, Label) and child.font_size == "16sp":
                child.text = "on" if active else "off"

class HomeScreen(Screen):
    update_event = None

    def on_enter(self):
        print("=== WEJŚCIE NA EKRAN HOME ===")
        # Aktualizacja danych przy wejściu na ekran
        print("Wymuszenie aktualizacji UI przy wejściu na ekran")
        self.update_from_state(None)

        # Uruchomienie aktualizacji z interwałem dostosowanym do platformy
        self.update_event = Clock.schedule_interval(self.update_from_state, _state_update_interval)
        print("=== ZAKOŃCZENIE WEJŚCIA NA EKRAN HOME ===")

    def on_leave(self):
        # Zatrzymanie aktualizacji przy wyjściu z ekranu
        if self.update_event:
            self.update_event.cancel()
            self.update_event = None

    def update_from_state(self, dt=None):
        """
        Aktualizuje UI na podstawie danych z aplikacji.
        Zoptymalizowana wersja, która aktualizuje tylko zmienione wartości.
        """
        try:
            # Pobieranie danych z aplikacji
            app = App.get_running_app()

            # Przechowywanie informacji o zmianach
            ui_changed = False

            # Aktualizacja wartości wskaźników tylko jeśli się zmieniły
            if hasattr(self.ids, 'battery_gauge'):
                new_text = f"{int(app.battery_level)}%"
                if self.ids.battery_gauge.text != new_text:
                    self.ids.battery_gauge.text = new_text
                    ui_changed = True

            if hasattr(self.ids, 'water_gauge'):
                new_text = f"{int(app.water_level)}%"
                if self.ids.water_gauge.text != new_text:
                    self.ids.water_gauge.text = new_text
                    ui_changed = True

            if hasattr(self.ids, 'fuel_gauge'):
                new_text = f"{int(app.fuel_level)}%"
                if self.ids.fuel_gauge.text != new_text:
                    self.ids.fuel_gauge.text = new_text
                    ui_changed = True

            # Aktualizacja pasków postępu
            if hasattr(self.ids, 'battery_progress'):
                if self.ids.battery_progress.value != app.battery_level:
                    self.ids.battery_progress.value = app.battery_level
                    ui_changed = True

            if hasattr(self.ids, 'water_progress'):
                if self.ids.water_progress.value != app.water_level:
                    self.ids.water_progress.value = app.water_level
                    ui_changed = True

            if hasattr(self.ids, 'fuel_progress'):
                if self.ids.fuel_progress.value != app.fuel_level:
                    self.ids.fuel_progress.value = app.fuel_level
                    ui_changed = True

            # Aktualizacja stanu przycisków
            if hasattr(self.ids, 'engine_status'):
                if self.ids.engine_status.text != app.engine_status:
                    self.ids.engine_status.text = app.engine_status
                    ui_changed = True

            if hasattr(self.ids, 'autopilot_status'):
                new_text = "on" if app.autopilot else "off"
                if self.ids.autopilot_status.text != new_text:
                    self.ids.autopilot_status.text = new_text
                    ui_changed = True

            # Aktualizacja kafelka oświetlenia
            any_light_active = (app.interior_light_active or
                               app.navigation_light_active or
                               app.deck_light_active)

            # Szukamy kafelka LightningTile i aktualizujemy jego status tylko jeśli się zmienił
            for child in self.walk():
                if isinstance(child, LightningTile):
                    # Sprawdzamy aktualny stan
                    current_state = None
                    for label_child in child.walk():
                        if isinstance(label_child, Label) and label_child.font_size == "16sp":
                            current_state = label_child.text == "on"
                            break

                    # Aktualizujemy tylko jeśli stan się zmienił
                    if current_state != any_light_active:
                        child.update_status(any_light_active)
                        ui_changed = True
                    break

            # Wymuszenie odświeżenia widoku tylko jeśli coś się zmieniło
            if ui_changed:
                self.canvas.ask_update()

        except Exception as e:
            print(f"Błąd aktualizacji UI w HomeScreen: {e}")
            import traceback
            traceback.print_exc()

    def toggle_engine(self):
        app = App.get_running_app()
        if app.engine_status == "active":
            app.engine_status = "inactive"
        else:
            app.engine_status = "active"
        app.save_state_to_file()

        # Natychmiastowa aktualizacja UI
        self.update_from_state(None)

    def toggle_autopilot(self):
        app = App.get_running_app()

        # Jeśli włączamy autopilota, upewnij się, że silnik jest aktywny
        if not app.autopilot and app.engine_status != "active":
            app.engine_status = "active"

        app.autopilot = not app.autopilot

        # Jeśli wyłączamy autopilota, nie wyłączaj silnika

        app.save_state_to_file()

        # Natychmiastowa aktualizacja UI
        self.update_from_state(None)

    def go_to_climate(self):
        self.manager.current = 'climate'

    def go_to_lightning(self):
        self.manager.current = 'lightning'

    def go_to_battery(self):
        self.manager.current = 'battery'

    def go_to_alarm(self):
        self.manager.current = 'alarm'

    def go_to_engine(self):
        self.manager.current = 'engine'

    def go_to_water(self):
        self.manager.current = 'water'

    def go_to_fuel(self):
        self.manager.current = 'fuel'

    def go_to_autopilot(self):
        self.manager.current = 'autopilot'

class ClimateScreen(Screen):
    set_temp = NumericProperty(22)
    fridge_temp = NumericProperty(4)
    auto_ac = BooleanProperty(True)
    fan_power = NumericProperty(50)
    ac_mode = StringProperty("off")
    current_internal_temp = NumericProperty(22)
    update_event = None

    def on_pre_enter(self):
        app = App.get_running_app()
        self.set_temp = app.climate_temp
        self.fridge_temp = app.fridge_temp
        self.auto_ac = app.auto_ac
        self.fan_power = app.fan_power
        self.ac_mode = app.ac_mode
        self.current_internal_temp = app.current_internal_temp

        # Uruchomienie aktualizacji z interwałem dostosowanym do platformy
        self.update_event = Clock.schedule_interval(self.update_from_state, _state_update_interval)

    def on_leave(self):
        # Zatrzymanie aktualizacji przy wyjściu z ekranu
        if self.update_event:
            self.update_event.cancel()
            self.update_event = None

    def update_from_state(self, dt=None):
        # Aktualizacja lokalnych właściwości na podstawie centralnego stanu
        app = App.get_running_app()
        self.set_temp = app.climate_temp
        self.fridge_temp = app.fridge_temp
        self.auto_ac = app.auto_ac
        self.fan_power = app.fan_power
        self.ac_mode = app.ac_mode
        self.current_internal_temp = app.current_internal_temp

        # Aktualizacja UI
        try:
            # Aktualizacja suwaka mocy wentylatora
            if hasattr(self.ids, 'fan_power_slider'):
                self.ids.fan_power_slider.value = self.fan_power

            # Aktualizacja statusu systemu
            if hasattr(self.ids, 'system_status'):
                if self.ac_mode == "heating":
                    self.ids.system_status.text = "HEATING"
                    self.ids.system_status.color = (1, 0.5, 0, 1)  # Pomarańczowy dla ogrzewania
                elif self.ac_mode == "cooling":
                    self.ids.system_status.text = "COOLING"
                    self.ids.system_status.color = (0, 0.7, 1, 1)  # Niebieski dla chłodzenia
                else:
                    self.ids.system_status.text = "OK"
                    self.ids.system_status.color = (0, 1, 0, 1)  # Zielony dla OK

            # Wymuszenie odświeżenia widoku
            self.canvas.ask_update()
        except Exception as e:
            print(f"Błąd aktualizacji UI klimatyzacji: {e}")

    def increase_temp(self):
        self.set_temp += 1
        app = App.get_running_app()
        app.climate_temp = self.set_temp
        app.save_state_to_file()

    def decrease_temp(self):
        self.set_temp -= 1
        app = App.get_running_app()
        app.climate_temp = self.set_temp
        app.save_state_to_file()

    def increase_fridge_temp(self):
        self.fridge_temp += 1
        app = App.get_running_app()
        app.fridge_temp = self.fridge_temp
        app.save_state_to_file()

    def decrease_fridge_temp(self):
        self.fridge_temp -= 1
        app = App.get_running_app()
        app.fridge_temp = self.fridge_temp
        app.save_state_to_file()

    def toggle_auto_ac(self):
        self.auto_ac = not self.auto_ac
        app = App.get_running_app()
        app.auto_ac = self.auto_ac
        app.save_state_to_file()

    def set_fan_power(self, value):
        # Aktualizacja mocy wentylatora tylko gdy auto_ac jest wyłączone
        if not self.auto_ac:
            self.fan_power = value
            app = App.get_running_app()
            app.fan_power = self.fan_power
            app.save_state_to_file()

class LightningScreen(Screen):
    interior_r = NumericProperty(255)
    interior_g = NumericProperty(255)
    interior_b = NumericProperty(255)
    interior_active = BooleanProperty(False)
    navigation_active = BooleanProperty(False)
    deck_active = BooleanProperty(False)
    interior_intensity = NumericProperty(100)  # domyślna wartość 100%

    def on_pre_enter(self):
        # Synchronizacja stanu z centralnym stanem
        app = App.get_running_app()
        self.interior_active = app.interior_light_active
        self.navigation_active = app.navigation_light_active
        self.deck_active = app.deck_light_active
        self.interior_r = app.interior_r
        self.interior_g = app.interior_g
        self.interior_b = app.interior_b
        self.interior_intensity = app.interior_intensity

        # Aktualizacja tekstu przycisków
        if hasattr(self.ids, 'interior_btn'):
            self.ids.interior_btn.text = 'on' if self.interior_active else 'off'
        if hasattr(self.ids, 'navigation_btn'):
            self.ids.navigation_btn.text = 'on' if self.navigation_active else 'off'
        if hasattr(self.ids, 'deck_btn'):
            self.ids.deck_btn.text = 'on' if self.deck_active else 'off'

    def update_from_state(self, dt=None):
        # Synchronizacja stanu z centralnym stanem
        app = App.get_running_app()

        # Zapisanie poprzednich wartości, aby wykryć zmiany
        old_interior_active = self.interior_active
        old_navigation_active = self.navigation_active
        old_deck_active = self.deck_active

        # Aktualizacja właściwości
        self.interior_active = app.interior_light_active
        self.navigation_active = app.navigation_light_active
        self.deck_active = app.deck_light_active
        self.interior_r = app.interior_r
        self.interior_g = app.interior_g
        self.interior_b = app.interior_b
        self.interior_intensity = app.interior_intensity

        # Aktualizacja tekstu przycisków
        if hasattr(self.ids, 'interior_btn'):
            self.ids.interior_btn.text = 'on' if self.interior_active else 'off'
        if hasattr(self.ids, 'navigation_btn'):
            self.ids.navigation_btn.text = 'on' if self.navigation_active else 'off'
        if hasattr(self.ids, 'deck_btn'):
            self.ids.deck_btn.text = 'on' if self.deck_active else 'off'



        # Aktualizacja sliderów RGB i intensywności
        if hasattr(self.ids, 'interior_r'):
            self.ids.interior_r.value = self.interior_r
        if hasattr(self.ids, 'interior_g'):
            self.ids.interior_g.value = self.interior_g
        if hasattr(self.ids, 'interior_b'):
            self.ids.interior_b.value = self.interior_b
        if hasattr(self.ids, 'interior_intensity'):
            self.ids.interior_intensity.value = self.interior_intensity

        # Wymuszenie odświeżenia widoku
        self.canvas.ask_update()

        # Jeśli zmieniły się stany oświetlenia, wymuszamy pełne odświeżenie widoku
        if (old_interior_active != self.interior_active or
            old_navigation_active != self.navigation_active or
            old_deck_active != self.deck_active):
            print(f"Zmiana stanu oświetlenia: interior={self.interior_active}, navigation={self.navigation_active}, deck={self.deck_active}")
            # Wymuszenie pełnego odświeżenia widoku
            self._trigger_layout()

    def on_interior_light(self):
        self.interior_active = not self.interior_active
        self.ids.interior_btn.text = 'on' if self.interior_active else 'off'

        # Aktualizacja centralnego stanu
        app = App.get_running_app()
        app.interior_light_active = self.interior_active
        app.save_state_to_file()

    def on_navigation_light(self):
        self.navigation_active = not self.navigation_active
        self.ids.navigation_btn.text = 'on' if self.navigation_active else 'off'

        # Aktualizacja centralnego stanu
        app = App.get_running_app()
        app.navigation_light_active = self.navigation_active
        app.save_state_to_file()

    def on_deck_light(self):
        self.deck_active = not self.deck_active
        self.ids.deck_btn.text = 'on' if self.deck_active else 'off'

        # Aktualizacja centralnego stanu
        app = App.get_running_app()
        app.deck_light_active = self.deck_active
        app.save_state_to_file()

    def update_interior_color(self):
        # This method will be called when RGB sliders change
        print(f"Interior RGB: {self.interior_r}, {self.interior_g}, {self.interior_b}")
        print(f"Intensity: {self.interior_intensity}%")

        # Aktualizacja centralnego stanu
        app = App.get_running_app()
        app.interior_r = self.interior_r
        app.interior_g = self.interior_g
        app.interior_b = self.interior_b
        app.interior_intensity = self.interior_intensity
        app.save_state_to_file()

    def go_to_home(self):
        self.manager.current = 'home'

class AlarmItem(BoxLayout):
    """Klasa reprezentująca pojedynczy alarm w interfejsie użytkownika"""
    alarm_id = StringProperty("")
    alarm_type = StringProperty("critical")  # critical, warning, info
    alarm_message = StringProperty("")
    alarm_timestamp = StringProperty("")
    alarm_source = StringProperty("")
    alarm_acknowledged = BooleanProperty(False)
    alarm_active = BooleanProperty(True)

    def __init__(self, **kwargs):
        super(AlarmItem, self).__init__(**kwargs)
        print(f"Tworzenie AlarmItem: {self.alarm_message} ({self.alarm_type})")


class BatteryScreen(Screen):
    sim_update_event = None

    def on_pre_enter(self):
        # Natychmiastowa aktualizacja UI
        self.update_from_state(0)

        # Uruchomienie aktualizacji UI z interwałem dostosowanym do platformy
        # RPi5: rzadsze aktualizacje dla oszczędności CPU
        self.sim_update_event = Clock.schedule_interval(self.update_from_state, _ui_update_interval)

    def on_leave(self):
        # Zatrzymanie symulacji przy wyjściu z ekranu
        if self.sim_update_event:
            self.sim_update_event.cancel()
            self.sim_update_event = None

    def update_from_state(self, dt=None):
        # Odświeżenie UI na podstawie danych z centralnego stanu
        try:
            app = App.get_running_app()



            # Aktualizacja wskaźników baterii
            if hasattr(self.ids, 'battery_level_label'):
                self.ids.battery_level_label.text = f"{int(app.battery_level)}%"

            # Aktualizacja statusu ładowania
            if hasattr(self.ids, 'charging_status'):
                self.ids.charging_status.text = 'CHARGING' if app.charging else 'DISCHARGING'
                self.ids.charging_status.color = (0, 0.8, 0, 1) if app.charging else (0.8, 0.3, 0.3, 1)

            # Aktualizacja przycisków źródła zasilania
            for child in self.walk():
                # Sprawdzamy, czy element jest przyciskiem ToggleButton
                if hasattr(child, 'group') and child.group == 'power_source' and hasattr(child, 'text'):
                    # Aktualizujemy stan przycisku na podstawie aktualnego źródła zasilania
                    if child.text == app.power_source:
                        child.state = 'down'
                    else:
                        child.state = 'normal'

            # Wymuszenie odświeżenia widoku
            self.canvas.ask_update()
        except Exception as e:
            print(f"Błąd aktualizacji UI baterii: {e}")
            import traceback
            traceback.print_exc()

    def change_power_source(self, source):
        # Zmiana źródła zasilania w centralnym stanie
        print(f"=== ROZPOCZĘCIE ZMIANY ŹRÓDŁA ZASILANIA W GŁÓWNEJ APLIKACJI NA {source} ===")

        app = App.get_running_app()

        # Zapisanie aktualnej wartości przed zmianą
        old_power_source = app.power_source
        print(f"Aktualne źródło zasilania: {old_power_source}")

        # Zmiana źródła zasilania
        app.power_source = source
        print(f"Nowe źródło zasilania: {app.power_source}")

        # Aktualizacja stanu ładowania na podstawie źródła zasilania
        if source == "Shore Power" or source == "Solar Panels":
            app.charging = True
        else:
            app.charging = False

        print(f"Stan ładowania: {app.charging}")

        # Zapisanie stanu do pliku
        app.save_state_to_file()

        print(f"Zmieniono źródło zasilania z {old_power_source} na {source}")
        print(f"=== ZAKOŃCZENIE ZMIANY ŹRÓDŁA ZASILANIA W GŁÓWNEJ APLIKACJI ===")

    def go_to_home(self):
        self.manager.current = 'home'

class EngineScreen(Screen):
    update_event = None

    def on_enter(self):
        print("=== WEJŚCIE NA EKRAN ENGINE ===")
        # Aktualizacja danych przy wejściu na ekran
        self.update_from_state(None)

        # Uruchomienie aktualizacji co 1 sekundę
        self.update_event = Clock.schedule_interval(self.update_from_state, 1)
        print("=== ZAKOŃCZENIE WEJŚCIA NA EKRAN ENGINE ===")

    def on_leave(self):
        # Zatrzymanie aktualizacji przy wyjściu z ekranu
        if self.update_event:
            self.update_event.cancel()
            self.update_event = None

    def update_from_state(self, dt=None):
        # Aktualizacja UI na podstawie danych z aplikacji
        try:
            app = App.get_running_app()

            # Aktualizacja statusu silnika
            if hasattr(self.ids, 'engine_status_label'):
                self.ids.engine_status_label.text = app.engine_status.upper()
                self.ids.engine_status_label.color = (0, 0.8, 0, 1) if app.engine_status == "active" else (0.8, 0.3, 0.3, 1)

            # Aktualizacja wartości przepustnicy (throttle)
            if hasattr(self.ids, 'throttle_slider'):
                self.ids.throttle_slider.value = app.engine_throttle

            # Aktualizacja RPM na podstawie przepustnicy
            if hasattr(self.ids, 'rpm_label'):
                # RPM zależy od przepustnicy i statusu silnika
                rpm = 0
                if app.engine_status == "active":
                    # Obliczenie RPM na podstawie przepustnicy (0-100%)
                    # Zakres RPM: 800 (bieg jałowy) do 3500 (maksymalne obroty)
                    rpm = 800 + (app.engine_throttle / 100) * 2700
                self.ids.rpm_label.text = f"{int(rpm)}"

                # Zmiana koloru RPM w zależności od wartości
                if hasattr(self.ids, 'rpm_label'):
                    if rpm > 3000:
                        self.ids.rpm_label.color = (0.9, 0.2, 0.2, 1)  # Czerwony dla wysokich obrotów
                    elif rpm > 2500:
                        self.ids.rpm_label.color = (0.9, 0.7, 0.2, 1)  # Żółty dla średnio-wysokich obrotów
                    else:
                        self.ids.rpm_label.color = (0.3, 0.8, 0.3, 1)  # Zielony dla normalnych obrotów

            # Aktualizacja temperatury silnika
            if hasattr(self.ids, 'temp_label'):
                self.ids.temp_label.text = f"{app.engine_temperature:.1f}°C"

                # Zmiana koloru temperatury w zależności od wartości
                if app.engine_temperature > 100:
                    self.ids.temp_label.color = (0.9, 0.2, 0.2, 1)  # Czerwony dla wysokiej temperatury

                    # Sprawdzenie czy istnieje alarm dla wysokiej temperatury silnika
                    if app.engine_temperature > 110 and app.alarm_manager_instance:
                        # Dodanie alarmu o wysokiej temperaturze silnika
                        app.add_alarm("critical", f"WARNING: Engine temperature critically high at {app.engine_temperature:.1f}°C!", "Engine")
                elif app.engine_temperature > 90:
                    self.ids.temp_label.color = (0.9, 0.7, 0.2, 1)  # Żółty dla podwyższonej temperatury
                else:
                    self.ids.temp_label.color = (0.3, 0.8, 0.3, 1)  # Zielony dla normalnej temperatury

            # Aktualizacja godzin pracy silnika
            if hasattr(self.ids, 'hours_label'):
                self.ids.hours_label.text = f"{int(app.engine_hours)} h"

            # Aktualizacja ciśnienia oleju (symulowane na podstawie RPM)
            if hasattr(self.ids, 'oil_pressure_label'):
                oil_pressure = 0
                if app.engine_status == "active":
                    # Ciśnienie oleju zależy od obrotów silnika
                    rpm = 800 + (app.engine_throttle / 100) * 2700
                    oil_pressure = 2.0 + (rpm / 3500) * 3.5
                self.ids.oil_pressure_label.text = f"{oil_pressure:.1f} bar"

                # Zmiana koloru ciśnienia oleju w zależności od wartości
                if oil_pressure < 2.0 and app.engine_status == "active":
                    self.ids.oil_pressure_label.color = (0.9, 0.2, 0.2, 1)  # Czerwony dla niskiego ciśnienia

                    # Dodanie alarmu o niskim ciśnieniu oleju
                    if app.alarm_manager_instance:
                        app.add_alarm("warning", f"WARNING: Low oil pressure at {oil_pressure:.1f} bar!", "Engine")
                elif oil_pressure > 5.0:
                    self.ids.oil_pressure_label.color = (0.9, 0.7, 0.2, 1)  # Żółty dla wysokiego ciśnienia
                else:
                    self.ids.oil_pressure_label.color = (0.3, 0.8, 0.3, 1)  # Zielony dla normalnego ciśnienia

            # Aktualizacja statusu systemu
            if hasattr(self.ids, 'system_status'):
                if app.engine_temperature > 100:
                    self.ids.system_status.text = "HIGH TEMP"
                    self.ids.system_status.color = (0.9, 0.2, 0.2, 1)  # Czerwony dla wysokiej temperatury
                elif app.engine_status == "active" and app.engine_throttle > 80:
                    self.ids.system_status.text = "HIGH RPM"
                    self.ids.system_status.color = (0.9, 0.7, 0.2, 1)  # Żółty dla wysokich obrotów
                else:
                    self.ids.system_status.text = "OK"
                    self.ids.system_status.color = (0, 1, 0, 1)  # Zielony dla OK

            # Wymuszenie odświeżenia widoku
            self.canvas.ask_update()
        except Exception as e:
            print(f"Błąd aktualizacji UI silnika: {e}")
            import traceback
            traceback.print_exc()

    def toggle_engine(self):
        app = App.get_running_app()
        if app.engine_status == "active":
            app.engine_status = "inactive"
            # Reset throttle when engine is turned off
            app.engine_throttle = 0

            # Dodanie alarmu o wyłączeniu silnika
            if app.alarm_manager_instance:
                app.add_alarm("info", "Engine stopped", "Engine")
        else:
            app.engine_status = "active"
            # Dodanie alarmu o uruchomieniu silnika
            if app.alarm_manager_instance:
                app.add_alarm("info", "Engine started", "Engine")

            # Sprawdzenie poziomu paliwa przy uruchomieniu silnika
            if app.fuel_level < 15 and app.alarm_manager_instance:
                app.add_alarm("critical", f"CRITICAL: Engine running with critically low fuel ({int(app.fuel_level)}%)! Engine may stop unexpectedly.", "Engine")

            # Zwiększenie licznika godzin pracy silnika
            app.engine_hours += 0.1

        app.save_state_to_file()

        # Natychmiastowa aktualizacja UI
        self.update_from_state(None)

    def set_throttle(self, value):
        app = App.get_running_app()
        # Only allow throttle changes when engine is active
        if app.engine_status == "active":
            app.engine_throttle = value

            # Symulacja zmiany temperatury silnika w zależności od przepustnicy
            # Temperatura rośnie wraz ze wzrostem przepustnicy
            base_temp = 65.0  # Temperatura bazowa przy biegu jałowym
            max_temp_increase = 45.0  # Maksymalny wzrost temperatury przy pełnej przepustnicy
            app.engine_temperature = base_temp + (value / 100.0) * max_temp_increase

            app.save_state_to_file()

            # Natychmiastowa aktualizacja UI
            self.update_from_state(None)

    def go_to_home(self):
        self.manager.current = 'home'


class WaterScreen(Screen):
    update_event = None
    water_pump_active = BooleanProperty(False)

    def on_enter(self):
        print("=== WEJŚCIE NA EKRAN WATER ===")
        # Aktualizacja danych przy wejściu na ekran
        self.update_from_state(None)

        # Uruchomienie aktualizacji co 1 sekundę
        self.update_event = Clock.schedule_interval(self.update_from_state, 1)
        print("=== ZAKOŃCZENIE WEJŚCIA NA EKRAN WATER ===")

    def on_leave(self):
        # Zatrzymanie aktualizacji przy wyjściu z ekranu
        if self.update_event:
            self.update_event.cancel()
            self.update_event = None

    def update_from_state(self, dt=None):
        # Aktualizacja UI na podstawie danych z aplikacji
        try:
            app = App.get_running_app()

            # Aktualizacja poziomu wody
            if hasattr(self.ids, 'water_level_label'):
                self.ids.water_level_label.text = f"{int(app.water_level)}%"

            if hasattr(self.ids, 'water_level_bar'):
                self.ids.water_level_bar.value = app.water_level

            # Wymuszenie odświeżenia widoku
            self.canvas.ask_update()
        except Exception as e:
            print(f"Błąd aktualizacji UI wody: {e}")
            import traceback
            traceback.print_exc()

    def toggle_water_pump(self):
        self.water_pump_active = not self.water_pump_active
        # Aktualizacja UI
        self.update_from_state(None)

    def go_to_home(self):
        self.manager.current = 'home'


class FuelScreen(Screen):
    update_event = None

    def on_enter(self):
        print("=== WEJŚCIE NA EKRAN FUEL ===")
        # Aktualizacja danych przy wejściu na ekran
        self.update_from_state(None)

        # Uruchomienie aktualizacji co 1 sekundę
        self.update_event = Clock.schedule_interval(self.update_from_state, 1)
        print("=== ZAKOŃCZENIE WEJŚCIA NA EKRAN FUEL ===")

    def on_leave(self):
        # Zatrzymanie aktualizacji przy wyjściu z ekranu
        if self.update_event:
            self.update_event.cancel()
            self.update_event = None

    def update_from_state(self, dt=None):
        # Aktualizacja UI na podstawie danych z aplikacji
        try:
            app = App.get_running_app()

            # Aktualizacja poziomu paliwa
            if hasattr(self.ids, 'fuel_level_label'):
                self.ids.fuel_level_label.text = f"{int(app.fuel_level)}%"

            if hasattr(self.ids, 'fuel_level_bar'):
                self.ids.fuel_level_bar.value = app.fuel_level

            # Wymuszenie odświeżenia widoku
            self.canvas.ask_update()
        except Exception as e:
            print(f"Błąd aktualizacji UI paliwa: {e}")
            import traceback
            traceback.print_exc()

    def go_to_home(self):
        self.manager.current = 'home'


class AutopilotScreen(Screen):
    update_event = None
    heading = NumericProperty(270)

    def on_enter(self):
        print("=== WEJŚCIE NA EKRAN AUTOPILOT ===")
        # Aktualizacja danych przy wejściu na ekran
        self.update_from_state(None)

        # Uruchomienie aktualizacji co 1 sekundę
        self.update_event = Clock.schedule_interval(self.update_from_state, 1)
        print("=== ZAKOŃCZENIE WEJŚCIA NA EKRAN AUTOPILOT ===")

    def on_leave(self):
        # Zatrzymanie aktualizacji przy wyjściu z ekranu
        if self.update_event:
            self.update_event.cancel()
            self.update_event = None

    def update_from_state(self, dt=None):
        # Aktualizacja UI na podstawie danych z aplikacji
        try:
            app = App.get_running_app()

            # Aktualizacja statusu autopilota
            if hasattr(self.ids, 'autopilot_status_label'):
                self.ids.autopilot_status_label.text = "ACTIVE" if app.autopilot else "INACTIVE"
                self.ids.autopilot_status_label.color = (0, 0.8, 0, 1) if app.autopilot else (0.8, 0.3, 0.3, 1)

            # Wymuszenie odświeżenia widoku
            self.canvas.ask_update()
        except Exception as e:
            print(f"Błąd aktualizacji UI autopilota: {e}")
            import traceback
            traceback.print_exc()

    def toggle_autopilot(self):
        app = App.get_running_app()

        # Jeśli włączamy autopilota, upewnij się, że silnik jest aktywny
        if not app.autopilot and app.engine_status != "active":
            app.engine_status = "active"

        app.autopilot = not app.autopilot

        # Jeśli wyłączamy autopilota, nie wyłączaj silnika

        app.save_state_to_file()

        # Natychmiastowa aktualizacja UI
        self.update_from_state(None)

    def adjust_heading(self, change):
        self.heading = (self.heading + change) % 360
        # W rzeczywistej aplikacji, tutaj byłaby aktualizacja centralnego stanu

    def go_to_home(self):
        self.manager.current = 'home'


class AlarmScreen(Screen):
    update_event = None

    def on_enter(self):
        print("=== WEJŚCIE NA EKRAN ALARMÓW ===")
        # Aktualizacja danych przy wejściu na ekran
        self.update_from_state(None)

        # Uruchomienie aktualizacji co 1 sekundę
        self.update_event = Clock.schedule_interval(self.update_from_state, 1)
        print("=== ZAKOŃCZENIE WEJŚCIA NA EKRAN ALARMÓW ===")

    def on_leave(self):
        # Zatrzymanie aktualizacji przy wyjściu z ekranu
        if self.update_event:
            self.update_event.cancel()
            self.update_event = None

    def update_from_state(self, dt=None):
        print("=== ROZPOCZĘCIE AKTUALIZACJI UI W ALARMSCREEN ===")
        try:
            # Pobieranie danych z aplikacji
            app = App.get_running_app()

            # Debugowanie alarmów
            self.debug_alarms()

            # Aktualizacja statusu systemu
            if hasattr(self.ids, 'system_status'):
                self.ids.system_status.text = "OK" if not app.has_active_alarms else "ALERT"
                self.ids.system_status.color = (0, 1, 0, 1) if not app.has_active_alarms else (1, 0, 0, 1)

            # Aktualizacja listy alarmów
            self.update_alarm_list()

            # Wymuszenie odświeżenia widoku
            self.canvas.ask_update()
            print("=== ZAKOŃCZENIE AKTUALIZACJI UI W ALARMSCREEN ===")
        except Exception as e:
            print(f"Błąd aktualizacji UI w AlarmScreen: {e}")
            import traceback
            traceback.print_exc()

    def debug_alarms(self):
        """Wyświetla informacje debugowania o alarmach"""
        app = App.get_running_app()

        # Sprawdzenie, czy menedżer alarmów jest zainicjalizowany
        if not app.alarm_manager_instance:
            print("UWAGA: Menedżer alarmów nie jest zainicjalizowany!")
            return

        # Pobranie alarmów
        active_alarms = app.get_active_alarms()

        # Wyświetlenie informacji o alarmach
        print(f"=== INFORMACJE O ALARMACH ===")
        print(f"Liczba aktywnych alarmów: {len(active_alarms)}")
        print(f"Flaga has_active_alarms: {app.has_active_alarms}")
        print(f"Flaga has_critical_alarms: {app.has_critical_alarms}")

        # Sprawdzenie zawartości pliku system_state.json
        try:
            import json
            import os
            state_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "system_state.json")
            if os.path.exists(state_file):
                with open(state_file, "r") as f:
                    state_data = json.load(f)
                    print(f"Zawartość system_state.json:")
                    if 'alarms' in state_data:
                        print(f"  Sekcja 'alarms' istnieje")
                        print(f"  Aktywne alarmy w pliku: {len(state_data['alarms'].get('active', []))}")
                        for alarm in state_data['alarms'].get('active', []):
                            print(f"    - {alarm.get('message', 'Brak wiadomości')} ({alarm.get('type', 'Brak typu')})")
                    else:
                        print(f"  Sekcja 'alarms' NIE istnieje w pliku!")
            else:
                print(f"Plik system_state.json nie istnieje!")
        except Exception as e:
            print(f"Błąd podczas sprawdzania pliku system_state.json: {e}")

        # Wyświetlenie szczegółów aktywnych alarmów
        if active_alarms:
            print("Aktywne alarmy:")
            for i, alarm in enumerate(active_alarms, 1):
                print(f"  {i}. ID: {alarm['id']}")
                print(f"     Typ: {alarm['type']}")
                print(f"     Źródło: {alarm['source']}")
                print(f"     Wiadomość: {alarm['message']}")
                print(f"     Czas: {alarm['timestamp']}")
                print(f"     Potwierdzony: {alarm.get('acknowledged', False)}")
        else:
            print("Brak aktywnych alarmów.")

    def update_alarm_list(self):
        """Aktualizuje listę alarmów - wyświetla wszystkie typy alarmów"""
        try:
            print("AlarmScreen.update_alarm_list: Rozpoczęcie aktualizacji listy alarmów")
            app = App.get_running_app()
            alarm_list = self.ids.alarm_list

            # Wyczyszczenie listy
            alarm_list.clear_widgets()

            # Wymuszenie wczytania alarmów z pliku
            if app.alarm_manager_instance:
                # Najpierw wykonaj wymuszenie zapisania aktualnego stanu alarmów do pliku
                app.save_state_to_file()

                # Wykonaj czyszczenie alarmów przed wyświetleniem
                print("AlarmScreen.update_alarm_list: Wykonywanie czyszczenia alarmów przed wyświetleniem")
                removed_count = app.alarm_manager_instance.clean_up_alarms()
                print(f"AlarmScreen.update_alarm_list: Usunięto {removed_count} alarmów podczas czyszczenia")

                # Aktualizacja właściwości alarmowych po czyszczeniu
                app.update_alarm_properties()

            # Pobranie wszystkich alarmów z menedżera alarmów (bez filtrowania)
            alarms = app.get_active_alarms()

            print(f"AlarmScreen.update_alarm_list: Znaleziono {len(alarms)} alarmów")

            # Sprawdzenie, czy wszystkie alarmy są faktycznie aktywne
            inactive_alarms = [alarm for alarm in alarms if not alarm.get('active', True)]
            if inactive_alarms:
                print(f"AlarmScreen.update_alarm_list: Znaleziono {len(inactive_alarms)} nieaktywnych alarmów, które zostaną usunięte")
                for alarm in inactive_alarms:
                    print(f"AlarmScreen.update_alarm_list: Usuwanie nieaktywnego alarmu: {alarm['id']}")
                    app.deactivate_alarm(alarm['id'])

                # Ponowne pobranie alarmów po usunięciu
                alarms = app.get_active_alarms()
                print(f"AlarmScreen.update_alarm_list: Po usunięciu nieaktywnych alarmów pozostało {len(alarms)} alarmów")

            # Sprawdzenie warunków systemowych, które mogą wymagać dezaktywacji alarmów
            state_data = state_manager.load_state()

            # Sprawdzenie poziomu baterii
            if 'battery_level' in state_data and state_data['battery_level'] >= 20:
                battery_alarms = [alarm for alarm in alarms
                                 if alarm['source'] == alarm_manager.SOURCE_BATTERY
                                 and ("low battery" in alarm['message'].lower() or "niski poziom baterii" in alarm['message'].lower())]

                if battery_alarms:
                    print(f"AlarmScreen.update_alarm_list: Znaleziono {len(battery_alarms)} alarmów baterii do usunięcia (poziom baterii: {state_data['battery_level']}%)")
                    for alarm in battery_alarms:
                        print(f"AlarmScreen.update_alarm_list: Usuwanie alarmu baterii: {alarm['id']}")
                        app.deactivate_alarm(alarm['id'])

                    # Ponowne pobranie alarmów po usunięciu
                    alarms = app.get_active_alarms()
                    print(f"AlarmScreen.update_alarm_list: Po usunięciu alarmów baterii pozostało {len(alarms)} alarmów")

            # Sprawdzenie temperatury silnika
            if 'engine_temperature' in state_data and state_data['engine_temperature'] <= 110:
                engine_temp_alarms = [alarm for alarm in alarms
                                     if alarm['source'] == alarm_manager.SOURCE_ENGINE
                                     and "temperature" in alarm['message'].lower()]

                if engine_temp_alarms:
                    print(f"AlarmScreen.update_alarm_list: Znaleziono {len(engine_temp_alarms)} alarmów temperatury silnika do usunięcia (temperatura: {state_data['engine_temperature']}°C)")
                    for alarm in engine_temp_alarms:
                        print(f"AlarmScreen.update_alarm_list: Usuwanie alarmu temperatury silnika: {alarm['id']}")
                        app.deactivate_alarm(alarm['id'])

                    # Ponowne pobranie alarmów po usunięciu
                    alarms = app.get_active_alarms()
                    print(f"AlarmScreen.update_alarm_list: Po usunięciu alarmów temperatury silnika pozostało {len(alarms)} alarmów")

            # Sprawdzenie poziomu wody
            if 'water_level' in state_data and state_data['water_level'] >= 10:
                water_alarms = [alarm for alarm in alarms
                               if alarm['source'] == alarm_manager.SOURCE_WATER
                               and "low water" in alarm['message'].lower()]

                if water_alarms:
                    print(f"AlarmScreen.update_alarm_list: Znaleziono {len(water_alarms)} alarmów poziomu wody do usunięcia (poziom wody: {state_data['water_level']}%)")
                    for alarm in water_alarms:
                        print(f"AlarmScreen.update_alarm_list: Usuwanie alarmu poziomu wody: {alarm['id']}")
                        app.deactivate_alarm(alarm['id'])

                    # Ponowne pobranie alarmów po usunięciu
                    alarms = app.get_active_alarms()
                    print(f"AlarmScreen.update_alarm_list: Po usunięciu alarmów poziomu wody pozostało {len(alarms)} alarmów")

            # Sprawdzenie poziomu paliwa
            if 'fuel_level' in state_data and state_data['fuel_level'] >= 15:
                fuel_alarms = [alarm for alarm in alarms
                              if alarm['source'] == alarm_manager.SOURCE_FUEL
                              and "low fuel" in alarm['message'].lower()]

                if fuel_alarms:
                    print(f"AlarmScreen.update_alarm_list: Znaleziono {len(fuel_alarms)} alarmów poziomu paliwa do usunięcia (poziom paliwa: {state_data['fuel_level']}%)")
                    for alarm in fuel_alarms:
                        print(f"AlarmScreen.update_alarm_list: Usuwanie alarmu poziomu paliwa: {alarm['id']}")
                        app.deactivate_alarm(alarm['id'])

                    # Ponowne pobranie alarmów po usunięciu
                    alarms = app.get_active_alarms()
                    print(f"AlarmScreen.update_alarm_list: Po usunięciu alarmów poziomu paliwa pozostało {len(alarms)} alarmów")

            # Dodanie widgetów alarmów do listy
            for alarm in alarms:
                # Sprawdzenie, czy alarm jest aktywny
                if not alarm.get('active', True):
                    print(f"AlarmScreen.update_alarm_list: Pomijanie nieaktywnego alarmu: {alarm['id']}")
                    continue

                # Tworzenie widgetu alarmu z wszystkimi potrzebnymi informacjami
                alarm_item = AlarmItem(
                    alarm_id=alarm['id'],
                    alarm_type=alarm['type'],
                    alarm_message=alarm['message'],
                    alarm_timestamp=alarm['timestamp'],
                    alarm_source=alarm['source'],
                    alarm_acknowledged=alarm.get('acknowledged', False),
                    alarm_active=alarm.get('active', True)
                )
                alarm_list.add_widget(alarm_item)

                # Dodanie separatora między alarmami
                if alarms.index(alarm) < len(alarms) - 1:
                    separator = Widget(
                        size_hint_y=None,
                        height=10
                    )
                    alarm_list.add_widget(separator)

                print(f"AlarmScreen.update_alarm_list: Dodano alarm: {alarm['message']} ({alarm['type']}), aktywny: {alarm.get('active', True)}, potwierdzony: {alarm.get('acknowledged', False)}")

            # Jeśli nie ma alarmów, dodaj informację
            if not alarms or all(not alarm.get('active', True) for alarm in alarms):
                label = Label(
                    text="No active alarms",
                    font_size=18,
                    size_hint_y=None,
                    height=50
                )
                alarm_list.add_widget(label)
                print("AlarmScreen.update_alarm_list: Dodano informację o braku alarmów")

            # Wymuszenie zapisania stanu po wszystkich zmianach
            app.save_state_to_file()

            print("AlarmScreen.update_alarm_list: Zakończenie aktualizacji listy alarmów")

        except Exception as e:
            print(f"AlarmScreen.update_alarm_list: Błąd aktualizacji listy alarmów: {e}")
            import traceback
            traceback.print_exc()

    def acknowledge_all_alarms(self):
        """Potwierdza wszystkie aktywne alarmy"""
        app = App.get_running_app()
        app.acknowledge_all_alarms()
        self.update_alarm_list()

    def deactivate_all_alarms(self):
        """Dezaktywuje wszystkie alarmy"""
        app = App.get_running_app()
        app.deactivate_all_alarms()
        self.update_alarm_list()

    def add_test_alarm(self):
        """Dodaje testowy alarm"""
        app = App.get_running_app()

        # Dodanie testowego alarmu
        app.add_alarm(
            alarm_manager.ALARM_CRITICAL,
            "This is a critical test alarm. Please acknowledge.",
            alarm_manager.SOURCE_SYSTEM
        )

        # Aktualizacja listy alarmów
        self.update_alarm_list()

        # Wyświetlenie informacji
        print("Dodano testowy alarm krytyczny.")

    def go_to_home(self):
        self.manager.current = 'home'

class MainApp(App):
    # Właściwości do bindowania w UI
    # Dane klimatyzacji
    climate_temp = NumericProperty(22)
    fridge_temp = NumericProperty(4)
    auto_ac = BooleanProperty(True)
    external_temp = NumericProperty(14)
    fan_power = NumericProperty(50)
    ac_mode = StringProperty("off")
    current_internal_temp = NumericProperty(22)

    # Dane oświetlenia
    interior_light_active = BooleanProperty(False)
    navigation_light_active = BooleanProperty(False)
    deck_light_active = BooleanProperty(False)
    interior_r = NumericProperty(255)
    interior_g = NumericProperty(255)
    interior_b = NumericProperty(255)
    interior_intensity = NumericProperty(100)

    # Dane baterii
    battery_level = NumericProperty(80)
    power_consumption = NumericProperty(15)
    charging = BooleanProperty(False)
    power_source = StringProperty("Battery")
    time_remaining = StringProperty("4h 20m")

    # Parametry symulacji baterii
    sim_discharge_rate = NumericProperty(0.5)
    sim_charge_rate = NumericProperty(1.0)

    # Dane zbiorników
    water_level = NumericProperty(50)
    fuel_level = NumericProperty(60)

    # Dane silnika i autopilota
    engine_status = StringProperty("active")
    engine_throttle = NumericProperty(0)
    engine_temperature = NumericProperty(75)
    engine_hours = NumericProperty(0)
    autopilot = BooleanProperty(False)
    emergency = BooleanProperty(False)

    # Dane alarmów
    has_active_alarms = BooleanProperty(False)
    has_critical_alarms = BooleanProperty(False)
    alarm_status = StringProperty("OK")

    # Aktualny czas
    current_time = StringProperty("")

    # Menedżer alarmów
    alarm_manager_instance = None

    # Pamięć podręczna stanu dla optymalizacji
    _state_cache = {}
    _last_state_update = 0
    _last_alarm_update = 0
    _state_update_interval = 5.0  # Interwał aktualizacji stanu w sekundach
    _alarm_update_interval = 2.0  # Interwał aktualizacji alarmów w sekundach

    def clean_all_alarms(self):
        """Czyści wszystkie nieaktywne alarmy i alarmy, które nie powinny być aktywne"""
        print("=== ROZPOCZĘCIE CZYSZCZENIA WSZYSTKICH ALARMÓW ===")
        if not self.alarm_manager_instance:
            print("Menedżer alarmów nie jest zainicjalizowany!")
            return 0

        # Najpierw wykonaj ogólne czyszczenie alarmów
        removed_count = self.alarm_manager_instance.clean_up_alarms()
        print(f"Usunięto {removed_count} alarmów podczas czyszczenia")

        # Wczytanie aktualnego stanu (używane przez alarm_manager.get_active_alarms())
        state_manager.load_state()

        # Pobranie wszystkich aktywnych alarmów
        active_alarms = self.alarm_manager_instance.get_active_alarms()
        alarms_to_remove = []

        # Sprawdzenie warunków systemowych, które mogą wymagać dezaktywacji alarmów
        # Sprawdzenie poziomu baterii
        if self.battery_level >= 20:
            print(f"Poziom baterii ({int(self.battery_level)}%) jest powyżej progu krytycznego (20%)")
            print("Usuwanie wszystkich alarmów baterii...")

            battery_alarms = [alarm for alarm in active_alarms
                             if alarm['source'] == alarm_manager.SOURCE_BATTERY
                             and ("low battery" in alarm['message'].lower() or "niski poziom baterii" in alarm['message'].lower())]

            if battery_alarms:
                print(f"Znaleziono {len(battery_alarms)} alarmów baterii do usunięcia")
                alarms_to_remove.extend([alarm['id'] for alarm in battery_alarms])
            else:
                print("Brak alarmów baterii do usunięcia")

        # Sprawdzenie temperatury silnika
        if hasattr(self, 'engine_temperature') and self.engine_temperature <= 110:
            print(f"Temperatura silnika ({self.engine_temperature:.1f}°C) jest poniżej progu krytycznego (110°C)")
            print("Usuwanie wszystkich alarmów temperatury silnika...")

            engine_temp_alarms = [alarm for alarm in active_alarms
                                 if alarm['source'] == alarm_manager.SOURCE_ENGINE
                                 and "temperature" in alarm['message'].lower()]

            if engine_temp_alarms:
                print(f"Znaleziono {len(engine_temp_alarms)} alarmów temperatury silnika do usunięcia")
                alarms_to_remove.extend([alarm['id'] for alarm in engine_temp_alarms])
            else:
                print("Brak alarmów temperatury silnika do usunięcia")

        # Sprawdzenie poziomu wody
        if self.water_level >= 10:
            print(f"Poziom wody ({int(self.water_level)}%) jest powyżej progu krytycznego (10%)")
            print("Usuwanie wszystkich alarmów poziomu wody...")

            water_alarms = [alarm for alarm in active_alarms
                           if alarm['source'] == alarm_manager.SOURCE_WATER
                           and "low water" in alarm['message'].lower()]

            if water_alarms:
                print(f"Znaleziono {len(water_alarms)} alarmów poziomu wody do usunięcia")
                alarms_to_remove.extend([alarm['id'] for alarm in water_alarms])
            else:
                print("Brak alarmów poziomu wody do usunięcia")

        # Sprawdzenie poziomu paliwa
        if self.fuel_level >= 15:
            print(f"Poziom paliwa ({int(self.fuel_level)}%) jest powyżej progu krytycznego (15%)")
            print("Usuwanie wszystkich alarmów poziomu paliwa...")

            fuel_alarms = [alarm for alarm in active_alarms
                          if alarm['source'] == alarm_manager.SOURCE_FUEL
                          and "low fuel" in alarm['message'].lower()]

            if fuel_alarms:
                print(f"Znaleziono {len(fuel_alarms)} alarmów poziomu paliwa do usunięcia")
                alarms_to_remove.extend([alarm['id'] for alarm in fuel_alarms])
            else:
                print("Brak alarmów poziomu paliwa do usunięcia")

        # Usunięcie wszystkich alarmów, które powinny być nieaktywne
        for alarm_id in alarms_to_remove:
            print(f"Usuwanie alarmu: {alarm_id}")
            self.alarm_manager_instance.deactivate_alarm(alarm_id)

        # Wymuszenie zapisania zmian
        self.alarm_manager_instance.save_alarms()
        print(f"Usunięto łącznie {len(alarms_to_remove)} alarmów")

        # Aktualizacja właściwości alarmowych
        self.update_alarm_properties()

        print("=== ZAKOŃCZENIE CZYSZCZENIA WSZYSTKICH ALARMÓW ===")
        return len(alarms_to_remove)

    def build(self):
        # Flaga do blokowania aktualizacji UI podczas programowych zmian wartości
        self.updating_ui = False

        # Inicjalizacja menedżera alarmów
        self.alarm_manager_instance = alarm_manager.get_instance()

        # Wczytanie początkowego stanu
        self.update_state_from_file(None)

        # Jednorazowe czyszczenie wszystkich alarmów przy starcie aplikacji
        removed_count = self.clean_all_alarms()

        # Uruchomienie monitora alarmów
        self.alarm_monitor = alarm_monitor.start_monitoring()

        # Uruchomienie cyklicznego wczytywania stanu z interwałem dostosowanym do platformy
        self.state_update_event = Clock.schedule_interval(self.update_state_from_file, _state_update_interval)

        # Uruchomienie cyklicznego czyszczenia alarmów z interwałem dostosowanym do platformy
        self.alarm_cleanup_event = Clock.schedule_interval(self.periodic_alarm_cleanup, _alarm_check_interval * 15)

        # Uruchomienie okresowego czyszczenia pamięci z interwałem dostosowanym do platformy
        self.gc_event = Clock.schedule_interval(self.perform_gc, _memory_cleanup_interval)

        # Utworzenie menedżera ekranów z wyłączoną animacją dla lepszej wydajności
        sm = ScreenManager(transition=NoTransition())
        sm.app = self
        sm.add_widget(HomeScreen(name="home"))
        sm.add_widget(ClimateScreen(name="climate"))
        sm.add_widget(LightningScreen(name="lightning"))
        sm.add_widget(BatteryScreen(name="battery"))
        sm.add_widget(AlarmScreen(name="alarm"))
        sm.add_widget(EngineScreen(name="engine"))
        sm.add_widget(WaterScreen(name="water"))
        sm.add_widget(FuelScreen(name="fuel"))
        sm.add_widget(AutopilotScreen(name="autopilot"))
        return sm

    def perform_gc(self, dt):
        """
        Wykonuje czyszczenie pamięci i optymalizację zasobów - zoptymalizowane dla RPi5.
        """
        current_time = time.time()
        if current_time - _performance_stats['last_gc_time'] >= _performance_stats['gc_interval']:
            # Uruchomienie garbage collectora
            collected = gc.collect()

            # Agresywniejsze czyszczenie pamięci podręcznej na RPi5
            if IS_RASPBERRY_PI:
                # Czyszczenie wszystkich cache'y Kivy
                Cache.remove('kv.image')
                Cache.remove('kv.texture')
                Cache.remove('kv.shader')
                Cache.remove('kv.atlas')
                Cache.remove('app.data')

                # Dodatkowe czyszczenie dla RPi5
                import weakref
                weakref.WeakSet()  # Wyczyść weak references

                # Optymalizacja pamięci dla ARM
                if hasattr(gc, 'set_threshold'):
                    gc.set_threshold(700, 10, 10)  # Bardziej agresywne GC na RPi5
            else:
                # Standardowe czyszczenie dla PC
                Cache.remove('kv.image')
                Cache.remove('kv.texture')

            # Aktualizacja statystyk
            _performance_stats['last_gc_time'] = current_time
            _performance_stats['memory_cleanups'] += 1

            # Logowanie tylko na RPi5 dla diagnostyki
            if IS_RASPBERRY_PI and collected > 0:
                print(f"GC: Zwolniono {collected} obiektów, cleanup #{_performance_stats['memory_cleanups']}")

    def save_state_to_file(self):
        """
        Zapisuje stan do pliku system_state.json.
        """
        try:
            print("=== ROZPOCZĘCIE ZAPISU STANU DO PLIKU ===")

            # Wczytanie aktualnego stanu, aby zachować sekcję 'alarms'
            current_state = state_manager.load_state()

            # Tworzenie słownika z danymi stanu
            state_data = {}
            for key in dir(self):
                if key.startswith('__') or key in ['root', 'updating_ui', 'state_update_event', 'time_update_event', 'alarm_manager_instance']:
                    continue

                value = getattr(self, key)
                if isinstance(value, (int, float, bool, str)) or value is None:
                    state_data[key] = value

            # Zachowanie sekcji 'alarms' z aktualnego stanu
            if 'alarms' in current_state:
                state_data['alarms'] = current_state['alarms']
            elif self.alarm_manager_instance:
                # Jeśli nie ma sekcji 'alarms' w pliku, ale menedżer alarmów jest zainicjalizowany,
                # pobierz alarmy bezpośrednio z menedżera alarmów
                print("Brak sekcji 'alarms' w pliku system_state.json - pobieranie alarmów z menedżera alarmów")
                alarms_data = {
                    'active': self.alarm_manager_instance.get_active_alarms(),
                    'history': self.alarm_manager_instance.get_alarm_history(),
                    'last_update': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                state_data['alarms'] = alarms_data
                print(f"Dodano sekcję 'alarms' do pliku system_state.json - aktywne alarmy: {len(alarms_data['active'])}")

            # Dodanie flag statusu alarmów
            if self.alarm_manager_instance:
                state_data['has_active_alarms'] = self.alarm_manager_instance.has_active_alarms()
                state_data['has_critical_alarms'] = self.alarm_manager_instance.has_critical_alarms()

            # Zapisanie stanu do pliku
            state_manager.save_state(state_data)

            print("=== ZAKOŃCZENIE ZAPISU STANU DO PLIKU ===")
        except Exception as e:
            print(f"Błąd zapisu stanu do pliku: {e}")
            import traceback
            traceback.print_exc()

    # Metoda update_time została usunięta, ponieważ ClockWidget jest teraz autonomiczny

    def update_state_from_file(self, dt=None):
        """
        Wczytuje stan z pliku system_state.json.
        Ta metoda jest wywoływana regularnie przez zegar.
        Zoptymalizowana o wykrywanie zmian i selektywną aktualizację UI.
        """
        # Sprawdzenie, czy minął wymagany interwał
        current_time = time.time()
        if dt is not None and current_time - self._last_state_update < self._state_update_interval:
            return  # Zbyt wcześnie na aktualizację

        # Ustawienie flagi, aby uniknąć pętli zwrotnej
        self.updating_ui = True

        try:
            # Sprawdzenie, czy stan się zmienił
            state_data = state_manager.load_state()

            # Jeśli stan się nie zmienił, pomijamy aktualizację
            if state_data == self._state_cache and dt is not None:
                self.updating_ui = False
                return

            # Zapisanie nowego stanu w pamięci podręcznej
            self._state_cache = state_data.copy()
            self._last_state_update = current_time

            # Wykrywanie zmian i selektywna aktualizacja
            changed_properties = []

            # Aktualizacja właściwości tylko jeśli się zmieniły
            for key, value in state_data.items():
                if hasattr(self, key):
                    try:
                        current_value = getattr(self, key)
                        # Aktualizuj tylko jeśli wartość się zmieniła
                        if current_value != value:
                            setattr(self, key, value)
                            changed_properties.append(key)
                    except AttributeError:
                        # Ignorujemy właściwości, które nie mają settera
                        pass

            # Jeśli nie było zmian, pomijamy dalszą aktualizację
            if not changed_properties and dt is not None:
                self.updating_ui = False
                return

            # Aktualizacja właściwości alarmowych tylko jeśli są zmiany w alarmach
            if 'alarms' in changed_properties or 'has_active_alarms' in changed_properties or 'has_critical_alarms' in changed_properties:
                self.update_alarm_properties()

            # Sprawdzenie systemu klimatyzacji
            if ('climate_temp' in changed_properties or 'current_internal_temp' in changed_properties or 'auto_ac' in changed_properties or 'ac_mode' in changed_properties) and self.alarm_manager_instance:
                self.check_climate_system()

            # Bezpośrednie sprawdzenie i usunięcie alarmów baterii
            if 'battery_level' in changed_properties and self.battery_level >= 20:
                # Pobranie aktywnych alarmów
                active_alarms = self.alarm_manager_instance.get_active_alarms()
                battery_alarms = [alarm for alarm in active_alarms
                                 if alarm['source'] == alarm_manager.SOURCE_BATTERY
                                 and ("low battery" in alarm['message'].lower() or "niski poziom baterii" in alarm['message'].lower())]

                if battery_alarms:
                    for alarm in battery_alarms:
                        self.alarm_manager_instance.deactivate_alarm(alarm['id'])

                    # Wymuszenie zapisania zmian
                    self.alarm_manager_instance.save_alarms()

                    # Aktualizacja właściwości alarmowych
                    self.update_alarm_properties()

                    # Odświeżenie ekranu alarmów, jeśli jest aktualnie wyświetlany
                    if hasattr(self, 'root') and self.root and self.root.current == 'alarm':
                        self.root.current_screen.update_alarm_list()

            # Aktualizacja UI tylko jeśli były zmiany lub jest to aktualnie wyświetlany ekran
            if hasattr(self, 'root') and self.root and (changed_properties or dt is None):
                # Aktualizujemy tylko aktualnie wyświetlany ekran
                current_screen = self.root.current_screen
                if hasattr(current_screen, 'update_from_state'):
                    current_screen.update_from_state(0)

                # Aktualizujemy pozostałe ekrany tylko jeśli były istotne zmiany
                important_changes = any(prop in changed_properties for prop in [
                    'battery_level', 'charging', 'power_source', 'climate_temp',
                    'external_temp', 'water_level', 'fuel_level', 'engine_status',
                    'engine_throttle', 'engine_temperature', 'engine_hours',
                    'autopilot', 'interior_light_active', 'navigation_light_active',
                    'deck_light_active', 'has_active_alarms', 'has_critical_alarms'
                ])

                if important_changes:
                    for screen_name in self.root.screen_names:
                        # Pomijamy aktualny ekran, bo już został zaktualizowany
                        if screen_name == self.root.current:
                            continue

                        screen = self.root.get_screen(screen_name)
                        if hasattr(screen, 'update_from_state'):
                            screen.update_from_state(0)

                # Zwiększenie licznika aktualizacji UI
                _performance_stats['ui_updates'] += 1
        except Exception as e:
            # Ciche logowanie błędów
            pass
        finally:
            # Zresetowanie flagi
            self.updating_ui = False

    def periodic_alarm_cleanup(self, *_):
        """Cykliczne czyszczenie alarmów"""
        try:
            print("=== ROZPOCZĘCIE CYKLICZNEGO CZYSZCZENIA ALARMÓW ===")

            # Sprawdzenie, czy menedżer alarmów jest zainicjalizowany
            if not self.alarm_manager_instance:
                print("Menedżer alarmów nie jest zainicjalizowany!")
                return

            # Sprawdzenie, czy są jakieś alarmy do usunięcia
            active_alarms = self.alarm_manager_instance.get_active_alarms()
            if not active_alarms:
                print("Brak aktywnych alarmów do sprawdzenia")
                return

            print(f"Znaleziono {len(active_alarms)} aktywnych alarmów do sprawdzenia")

            # Sprawdzenie warunków systemowych, które mogą wymagać dezaktywacji alarmów
            alarms_to_remove = []

            # Sprawdzenie poziomu baterii
            if self.battery_level >= 20:
                battery_alarms = [alarm for alarm in active_alarms
                                 if alarm['source'] == alarm_manager.SOURCE_BATTERY
                                 and ("low battery" in alarm['message'].lower() or "niski poziom baterii" in alarm['message'].lower())]

                if battery_alarms:
                    print(f"Znaleziono {len(battery_alarms)} alarmów baterii do usunięcia (poziom baterii: {self.battery_level}%)")
                    alarms_to_remove.extend([alarm['id'] for alarm in battery_alarms])

            # Sprawdzenie temperatury silnika
            if hasattr(self, 'engine_temperature') and self.engine_temperature <= 110:
                engine_temp_alarms = [alarm for alarm in active_alarms
                                     if alarm['source'] == alarm_manager.SOURCE_ENGINE
                                     and "temperature" in alarm['message'].lower()]

                if engine_temp_alarms:
                    print(f"Znaleziono {len(engine_temp_alarms)} alarmów temperatury silnika do usunięcia (temperatura: {self.engine_temperature}°C)")
                    alarms_to_remove.extend([alarm['id'] for alarm in engine_temp_alarms])

            # Sprawdzenie systemu klimatyzacji
            fan_power = getattr(self, 'fan_power', 0)
            ac_mode = getattr(self, 'ac_mode', 'off')

            # Sprawdzamy czy klimatyzacja jest nieaktywna (auto wyłączone i wentylator wyłączony lub tryb off)
            if hasattr(self, 'auto_ac') and not self.auto_ac and (fan_power <= 0 or ac_mode == 'off'):
                print("Klimatyzacja jest nieaktywna")
                print("Usuwanie wszystkich alarmów systemu klimatyzacji...")

                climate_alarms = [alarm for alarm in active_alarms
                                 if alarm['source'] == 'Climate'
                                 and "climate control system" in alarm['message'].lower()]

                if climate_alarms:
                    print(f"Znaleziono {len(climate_alarms)} alarmów systemu klimatyzacji do usunięcia")
                    alarms_to_remove.extend([alarm['id'] for alarm in climate_alarms])
                else:
                    print("Brak alarmów systemu klimatyzacji do usunięcia")
            elif hasattr(self, 'current_internal_temp') and hasattr(self, 'climate_temp'):
                temp_diff = abs(self.climate_temp - self.current_internal_temp)
                if temp_diff <= 3.0:
                    print(f"Różnica temperatur ({temp_diff:.1f}°C) jest w dopuszczalnym zakresie")
                    print("Usuwanie wszystkich alarmów systemu klimatyzacji...")

                    climate_alarms = [alarm for alarm in active_alarms
                                     if alarm['source'] == 'Climate'
                                     and "climate control system" in alarm['message'].lower()]

                    if climate_alarms:
                        print(f"Znaleziono {len(climate_alarms)} alarmów systemu klimatyzacji do usunięcia")
                        alarms_to_remove.extend([alarm['id'] for alarm in climate_alarms])
                    else:
                        print("Brak alarmów systemu klimatyzacji do usunięcia")

            # Sprawdzenie poziomu wody
            if self.water_level >= 10:
                water_alarms = [alarm for alarm in active_alarms
                               if alarm['source'] == alarm_manager.SOURCE_WATER
                               and "low water" in alarm['message'].lower()]

                if water_alarms:
                    print(f"Znaleziono {len(water_alarms)} alarmów poziomu wody do usunięcia (poziom wody: {self.water_level}%)")
                    alarms_to_remove.extend([alarm['id'] for alarm in water_alarms])

            # Sprawdzenie poziomu paliwa
            if self.fuel_level >= 15:
                fuel_alarms = [alarm for alarm in active_alarms
                              if alarm['source'] == alarm_manager.SOURCE_FUEL
                              and "low fuel" in alarm['message'].lower()]

                if fuel_alarms:
                    print(f"Znaleziono {len(fuel_alarms)} alarmów poziomu paliwa do usunięcia (poziom paliwa: {self.fuel_level}%)")
                    alarms_to_remove.extend([alarm['id'] for alarm in fuel_alarms])

            # Usunięcie wszystkich alarmów, które powinny być nieaktywne
            if alarms_to_remove:
                print(f"Usuwanie {len(alarms_to_remove)} alarmów...")
                for alarm_id in alarms_to_remove:
                    print(f"Usuwanie alarmu: {alarm_id}")
                    self.alarm_manager_instance.deactivate_alarm(alarm_id)

                # Wymuszenie zapisania zmian
                self.alarm_manager_instance.save_alarms()

                # Aktualizacja właściwości alarmowych
                self.update_alarm_properties()

                # Odświeżenie ekranu alarmów, jeśli jest aktualnie wyświetlany
                if hasattr(self, 'root') and self.root and self.root.current == 'alarm':
                    self.root.current_screen.update_alarm_list()

                print(f"Usunięto {len(alarms_to_remove)} alarmów")
            else:
                print("Brak alarmów do usunięcia")

            print("=== ZAKOŃCZENIE CYKLICZNEGO CZYSZCZENIA ALARMÓW ===")
        except Exception as e:
            print(f"Błąd podczas cyklicznego czyszczenia alarmów: {e}")
            import traceback
            traceback.print_exc()

    def on_stop(self):
        # Zatrzymanie cyklicznego wczytywania stanu
        if hasattr(self, 'state_update_event'):
            self.state_update_event.cancel()

        # Zatrzymanie cyklicznego czyszczenia alarmów
        if hasattr(self, 'alarm_cleanup_event'):
            self.alarm_cleanup_event.cancel()

        # Zatrzymanie monitora alarmów
        if hasattr(self, 'alarm_monitor'):
            print("=== ZATRZYMYWANIE MONITORA ALARMÓW ===")
            alarm_monitor.stop_monitoring()
            print("=== MONITOR ALARMÓW ZATRZYMANY ===")

        # Nie zapisujemy stanu przy zamknięciu aplikacji - panel kontrolny jest odpowiedzialny za stan

    # Metody obsługi alarmów
    def add_alarm(self, alarm_type, message, source):
        """Dodaje nowy alarm do systemu"""
        if self.alarm_manager_instance:
            return self.alarm_manager_instance.add_alarm(alarm_type, message, source)
        return None

    def acknowledge_alarm(self, alarm_id):
        """Potwierdza alarm o podanym ID"""
        if self.alarm_manager_instance:
            result = self.alarm_manager_instance.acknowledge_alarm(alarm_id)
            # Aktualizacja właściwości alarmowych
            self.update_alarm_properties()

            # Odświeżenie ekranu alarmów, jeśli jest aktualnie wyświetlany
            if hasattr(self, 'root') and self.root and self.root.current == 'alarm':
                self.root.current_screen.update_alarm_list()

            return result
        return False

    def acknowledge_all_alarms(self):
        """Potwierdza wszystkie aktywne alarmy"""
        if self.alarm_manager_instance:
            self.alarm_manager_instance.acknowledge_all_alarms()
            # Aktualizacja właściwości alarmowych
            self.update_alarm_properties()

            # Odświeżenie ekranu alarmów, jeśli jest aktualnie wyświetlany
            if hasattr(self, 'root') and self.root and self.root.current == 'alarm':
                self.root.current_screen.update_alarm_list()

    def deactivate_alarm(self, alarm_id):
        """Dezaktywuje alarm o podanym ID"""
        if self.alarm_manager_instance:
            result = self.alarm_manager_instance.deactivate_alarm(alarm_id)
            # Aktualizacja właściwości alarmowych
            self.update_alarm_properties()

            # Odświeżenie ekranu alarmów, jeśli jest aktualnie wyświetlany
            if hasattr(self, 'root') and self.root and self.root.current == 'alarm':
                self.root.current_screen.update_alarm_list()

            return result
        return False

    def deactivate_alarm_if_inactive(self, alarm_id, is_active):
        """Dezaktywuje alarm, jeśli jest nieaktywny"""
        if self.alarm_manager_instance:
            result = self.alarm_manager_instance.deactivate_alarm_if_inactive(alarm_id, is_active)
            if result:
                # Aktualizacja właściwości alarmowych
                self.update_alarm_properties()

                # Odświeżenie ekranu alarmów, jeśli jest aktualnie wyświetlany
                if hasattr(self, 'root') and self.root and self.root.current == 'alarm':
                    self.root.current_screen.update_alarm_list()

            return result
        return False

    def deactivate_all_alarms(self):
        """Dezaktywuje wszystkie alarmy"""
        if self.alarm_manager_instance:
            self.alarm_manager_instance.deactivate_all_alarms()
            # Aktualizacja właściwości alarmowych
            self.update_alarm_properties()

            # Odświeżenie ekranu alarmów, jeśli jest aktualnie wyświetlany
            if hasattr(self, 'root') and self.root and self.root.current == 'alarm':
                self.root.current_screen.update_alarm_list()

    def get_active_alarms(self, alarm_types=None):
        """Zwraca listę aktywnych alarmów, opcjonalnie filtrowaną po typach"""
        if self.alarm_manager_instance:
            alarms = self.alarm_manager_instance.get_active_alarms(alarm_types)
            print(f"MainApp.get_active_alarms: znaleziono {len(alarms)} alarmów")
            for alarm in alarms:
                print(f"  - {alarm.get('message', 'Brak wiadomości')} ({alarm.get('type', 'Brak typu')})")
            return alarms
        print("MainApp.get_active_alarms: alarm_manager_instance jest None!")
        return []

    def get_alarm_history(self, limit=50, alarm_types=None):
        """Zwraca historię alarmów, opcjonalnie filtrowaną po typach"""
        if self.alarm_manager_instance:
            return self.alarm_manager_instance.get_alarm_history(limit, alarm_types)
        return []

    def update_alarm(self, alarm_id, message):
        """Aktualizuje treść alarmu"""
        if self.alarm_manager_instance:
            return self.alarm_manager_instance.update_alarm(alarm_id, message)
        return False

    def update_alarm_properties(self):
        """Aktualizuje właściwości alarmowe na podstawie stanu menedżera alarmów"""
        if self.alarm_manager_instance:
            self.has_active_alarms = self.alarm_manager_instance.has_active_alarms()
            self.has_critical_alarms = self.alarm_manager_instance.has_critical_alarms()
            self.alarm_status = self.alarm_manager_instance.get_system_status()

    def check_battery_level(self):
        """Sprawdza poziom baterii i dodaje/aktualizuje alarm, gdy poziom jest poniżej 20%"""
        print(f"MainApp.check_battery_level: Sprawdzanie poziomu baterii: {self.battery_level}%")
        if self.alarm_manager_instance:
            # Pobranie aktywnych alarmów baterii
            existing_alarms = self.get_active_alarms()
            battery_alarm_id = None
            battery_alarms = []

            # Sprawdzenie, czy istnieje alarm o niskim poziomie baterii
            for alarm in existing_alarms:
                if alarm['source'] == alarm_manager.SOURCE_BATTERY and ("low battery" in alarm['message'].lower() or "niski poziom baterii" in alarm['message'].lower()):
                    battery_alarm_id = alarm['id']
                    battery_alarms.append(alarm)
                    print(f"MainApp.check_battery_level: Znaleziono istniejący alarm baterii: {alarm['id']}, wiadomość: {alarm['message']}, aktywny: {alarm.get('active', True)}")

            print(f"MainApp.check_battery_level: Znaleziono {len(battery_alarms)} alarmów baterii")

            # Sprawdzenie, czy poziom baterii jest poniżej 20%
            if self.battery_level < 20:
                # Przygotowanie treści alarmu
                alarm_message = f"Critically low battery level! Only {int(self.battery_level)}% power remaining."

                if not battery_alarm_id:
                    # Jeśli nie ma jeszcze alarmu, dodaj go
                    battery_alarm_id = self.alarm_manager_instance.add_battery_alarm(
                        alarm_message,
                        alarm_manager.ALARM_CRITICAL
                    )
                    print(f"MainApp.check_battery_level: Dodano alarm krytyczny: niski poziom baterii ({int(self.battery_level)}%)")
                else:
                    # Jeśli alarm już istnieje, zaktualizuj jego treść
                    self.alarm_manager_instance.update_battery_alarm(
                        battery_alarm_id,
                        alarm_message
                    )
                    print(f"MainApp.check_battery_level: Zaktualizowano alarm krytyczny: niski poziom baterii ({int(self.battery_level)}%)")

                # Aktualizacja właściwości alarmowych
                self.update_alarm_properties()
            elif self.battery_level >= 20:
                # Jeśli poziom baterii wzrósł powyżej 20%, dezaktywuj wszystkie alarmy baterii
                print(f"MainApp.check_battery_level: Poziom baterii ({int(self.battery_level)}%) jest powyżej progu krytycznego (20%)")

                if battery_alarms:
                    print(f"MainApp.check_battery_level: Dezaktywacja {len(battery_alarms)} alarmów baterii")
                    for alarm in battery_alarms:
                        print(f"MainApp.check_battery_level: Dezaktywacja alarmu baterii {alarm['id']} - poziom baterii wzrósł do {int(self.battery_level)}%")
                        result = self.alarm_manager_instance.deactivate_alarm(alarm['id'])
                        print(f"MainApp.check_battery_level: Wynik dezaktywacji alarmu {alarm['id']}: {result}")
                else:
                    print("MainApp.check_battery_level: Brak alarmów baterii do dezaktywacji")

                # Aktualizacja właściwości alarmowych
                self.update_alarm_properties()

                # Odświeżenie ekranu alarmów, jeśli jest aktualnie wyświetlany
                if hasattr(self, 'root') and self.root and self.root.current == 'alarm':
                    self.root.current_screen.update_alarm_list()

    def check_fuel_level(self):
        """Sprawdza poziom paliwa i dodaje/dezaktywuje alarmy"""
        if self.alarm_manager_instance:
            # Pobranie aktywnych alarmów paliwa
            existing_alarms = self.get_active_alarms()
            fuel_level_alarm_id = None

            # Sprawdzenie, czy istnieje alarm o niskim poziomie paliwa
            for alarm in existing_alarms:
                if alarm['source'] == alarm_manager.SOURCE_FUEL and "low fuel" in alarm['message'].lower():
                    fuel_level_alarm_id = alarm['id']
                    break

            # Krytyczny poziom paliwa - poniżej 15%
            if self.fuel_level < 15:
                # Przygotowanie treści alarmu
                alarm_message = f"Low fuel level! Only {int(self.fuel_level)}% remaining."

                if not fuel_level_alarm_id:
                    # Jeśli nie ma jeszcze alarmu, dodaj go
                    fuel_level_alarm_id = self.alarm_manager_instance.add_fuel_alarm(
                        alarm_message,
                        alarm_manager.ALARM_WARNING
                    )
                    print(f"Dodano alarm ostrzegawczy: niski poziom paliwa ({int(self.fuel_level)}%)")
                else:
                    # Jeśli alarm już istnieje, zaktualizuj jego treść
                    self.alarm_manager_instance.update_alarm(
                        fuel_level_alarm_id,
                        alarm_message
                    )
                    print(f"Zaktualizowano alarm ostrzegawczy: niski poziom paliwa ({int(self.fuel_level)}%)")

                # Aktualizacja właściwości alarmowych
                self.update_alarm_properties()
            elif fuel_level_alarm_id and self.fuel_level >= 15:
                # Jeśli poziom paliwa wzrósł powyżej krytycznego poziomu, dezaktywuj alarm
                self.alarm_manager_instance.deactivate_alarm(fuel_level_alarm_id)
                print(f"Dezaktywowano alarm o niskim poziomie paliwa - poziom wzrósł do {int(self.fuel_level)}%")

                # Aktualizacja właściwości alarmowych
                self.update_alarm_properties()

    def check_water_level(self):
        """Sprawdza poziom wody i dodaje/dezaktywuje alarmy"""
        if self.alarm_manager_instance:
            # Pobranie aktywnych alarmów wody
            existing_alarms = self.get_active_alarms()
            water_level_alarm_id = None

            # Sprawdzenie, czy istnieje alarm o niskim poziomie wody
            for alarm in existing_alarms:
                if alarm['source'] == alarm_manager.SOURCE_WATER and "low water" in alarm['message'].lower():
                    water_level_alarm_id = alarm['id']
                    break

            # Krytyczny poziom wody - poniżej 10%
            if self.water_level < 10:
                # Przygotowanie treści alarmu
                alarm_message = f"Critically low water level! Only {int(self.water_level)}% remaining."

                if not water_level_alarm_id:
                    # Jeśli nie ma jeszcze alarmu, dodaj go
                    water_level_alarm_id = self.alarm_manager_instance.add_water_alarm(
                        alarm_message,
                        alarm_manager.ALARM_WARNING
                    )
                    print(f"Dodano alarm ostrzegawczy: niski poziom wody ({int(self.water_level)}%)")
                else:
                    # Jeśli alarm już istnieje, zaktualizuj jego treść
                    self.alarm_manager_instance.update_alarm(
                        water_level_alarm_id,
                        alarm_message
                    )
                    print(f"Zaktualizowano alarm ostrzegawczy: niski poziom wody ({int(self.water_level)}%)")

                # Aktualizacja właściwości alarmowych
                self.update_alarm_properties()
            elif water_level_alarm_id and self.water_level >= 10:
                # Jeśli poziom wody wzrósł powyżej krytycznego poziomu, dezaktywuj alarm
                self.alarm_manager_instance.deactivate_alarm(water_level_alarm_id)
                print(f"Dezaktywowano alarm o niskim poziomie wody - poziom wzrósł do {int(self.water_level)}%")

                # Aktualizacja właściwości alarmowych
                self.update_alarm_properties()

    def check_engine_temperature(self):
        """Sprawdza temperaturę silnika i dodaje/dezaktywuje alarmy"""
        if self.alarm_manager_instance and hasattr(self, 'engine_temperature'):
            # Pobranie aktywnych alarmów silnika
            existing_alarms = self.get_active_alarms()
            engine_temp_alarm_id = None

            # Sprawdzenie, czy istnieje alarm o wysokiej temperaturze silnika
            for alarm in existing_alarms:
                if alarm['source'] == alarm_manager.SOURCE_ENGINE and "temperature" in alarm['message'].lower():
                    engine_temp_alarm_id = alarm['id']
                    break

            # Krytyczna temperatura silnika - powyżej 110°C
            if hasattr(self, 'engine_temperature') and self.engine_temperature > 110:
                # Przygotowanie treści alarmu
                alarm_message = f"Critical engine temperature! Current temperature: {self.engine_temperature:.1f}°C"

                if not engine_temp_alarm_id:
                    # Jeśli nie ma jeszcze alarmu, dodaj go
                    engine_temp_alarm_id = self.alarm_manager_instance.add_engine_alarm(
                        alarm_message,
                        alarm_manager.ALARM_CRITICAL
                    )
                    print(f"Dodano alarm krytyczny: wysoka temperatura silnika ({self.engine_temperature:.1f}°C)")
                else:
                    # Jeśli alarm już istnieje, zaktualizuj jego treść
                    self.alarm_manager_instance.update_alarm(
                        engine_temp_alarm_id,
                        alarm_message
                    )
                    print(f"Zaktualizowano alarm krytyczny: wysoka temperatura silnika ({self.engine_temperature:.1f}°C)")

                # Aktualizacja właściwości alarmowych
                self.update_alarm_properties()
            elif engine_temp_alarm_id and hasattr(self, 'engine_temperature') and self.engine_temperature <= 110:
                # Jeśli temperatura silnika spadła poniżej krytycznego poziomu, dezaktywuj alarm
                self.alarm_manager_instance.deactivate_alarm(engine_temp_alarm_id)
                print(f"Dezaktywowano alarm o wysokiej temperaturze silnika - temperatura spadła do {self.engine_temperature:.1f}°C")

                # Aktualizacja właściwości alarmowych
                self.update_alarm_properties()

    def check_climate_system(self):
        """Sprawdza system klimatyzacji i dodaje/dezaktywuje alarmy"""
        if not self.alarm_manager_instance:
            print("Menedżer alarmów nie jest zainicjalizowany!")
            return

        # Sprawdzenie, czy mamy wszystkie potrzebne dane
        if not hasattr(self, 'climate_temp') or not hasattr(self, 'current_internal_temp') or not hasattr(self, 'auto_ac'):
            print("Brak wymaganych danych klimatyzacji!")
            return

        print(f"Sprawdzanie systemu klimatyzacji: zadana={self.climate_temp:.1f}°C, aktualna={self.current_internal_temp:.1f}°C, auto_ac={self.auto_ac}")

        # Sprawdzamy zarówno dla trybu auto jak i manualnego, ale tylko gdy klimatyzacja jest aktywna
        fan_power = getattr(self, 'fan_power', 0)
        ac_mode = getattr(self, 'ac_mode', 'off')

        if not self.auto_ac and (fan_power <= 0 or ac_mode == 'off'):
            print("Klimatyzacja jest nieaktywna, pomijanie sprawdzania")
            return

        # Pobranie aktywnych alarmów klimatyzacji
        existing_alarms = self.get_active_alarms()
        climate_alarms = {
            'warning': None,
            'caution': None
        }

        # Sprawdzenie, czy istnieją alarmy klimatyzacji
        for alarm in existing_alarms:
            if alarm['source'] == 'Climate' and "climate control system" in alarm['message'].lower():
                if alarm['type'] == alarm_manager.ALARM_WARNING:
                    climate_alarms['warning'] = alarm['id']
                elif alarm['type'] == alarm_manager.ALARM_CAUTION:
                    climate_alarms['caution'] = alarm['id']
                print(f"Znaleziono istniejący alarm klimatyzacji: {alarm['id']}, typ: {alarm['type']}, wiadomość: {alarm['message']}")

        # Obliczenie różnicy temperatur
        temp_diff = abs(self.climate_temp - self.current_internal_temp)
        ac_mode = getattr(self, 'ac_mode', 'off')

        # Sprawdzenie, czy system klimatyzacji działa prawidłowo
        if temp_diff > 5.0 and (
            (ac_mode == "heating" and self.climate_temp > self.current_internal_temp) or
            (ac_mode == "cooling" and self.climate_temp < self.current_internal_temp)
        ):
            # Przygotowanie treści alarmu
            alarm_message = f"WARNING: Climate control system may be malfunctioning. Target: {self.climate_temp:.1f}°C, Actual: {self.current_internal_temp:.1f}°C"

            if not climate_alarms['warning']:
                # Jeśli nie ma jeszcze alarmu ostrzegawczego, dodaj go
                climate_alarms['warning'] = self.alarm_manager_instance.add_alarm(
                    alarm_manager.ALARM_WARNING,
                    alarm_message,
                    'Climate',
                    category=alarm_manager.CATEGORY_B  # Kategoria B dla ostrzeżeń
                )
                print(f"Dodano alarm ostrzegawczy: problem z systemem klimatyzacji (różnica: {temp_diff:.1f}°C)")
            else:
                # Jeśli alarm już istnieje, zaktualizuj jego treść
                self.alarm_manager_instance.update_alarm(
                    climate_alarms['warning'],
                    alarm_message
                )
                print(f"Zaktualizowano alarm ostrzegawczy: problem z systemem klimatyzacji (różnica: {temp_diff:.1f}°C)")

            # Dezaktywacja alarmów o niższym priorytecie
            if climate_alarms['caution']:
                self.alarm_manager_instance.deactivate_alarm(climate_alarms['caution'])
                print(f"Dezaktywowano alarm ostrożności klimatyzacji - zastąpiony alarmem ostrzegawczym")

            # Aktualizacja właściwości alarmowych
            self.update_alarm_properties()
        elif temp_diff > 3.0 and temp_diff <= 5.0 and (
            (ac_mode == "heating" and self.climate_temp > self.current_internal_temp) or
            (ac_mode == "cooling" and self.climate_temp < self.current_internal_temp)
        ):
            # Przygotowanie treści alarmu
            alarm_message = f"CAUTION: Climate control system response slow. Target: {self.climate_temp:.1f}°C, Actual: {self.current_internal_temp:.1f}°C"

            # Dezaktywacja alarmów o wyższym priorytecie, jeśli stan się poprawił
            if climate_alarms['warning']:
                self.alarm_manager_instance.deactivate_alarm(climate_alarms['warning'])
                print(f"Dezaktywowano alarm ostrzegawczy klimatyzacji - stan poprawił się do poziomu ostrożności")

            if not climate_alarms['caution']:
                # Jeśli nie ma jeszcze alarmu ostrożności, dodaj go
                climate_alarms['caution'] = self.alarm_manager_instance.add_alarm(
                    alarm_manager.ALARM_CAUTION,
                    alarm_message,
                    'Climate',
                    category=alarm_manager.CATEGORY_C  # Kategoria C dla ostrożności
                )
                print(f"Dodano alarm ostrożności: powolna reakcja systemu klimatyzacji (różnica: {temp_diff:.1f}°C)")
            else:
                # Jeśli alarm już istnieje, zaktualizuj jego treść
                self.alarm_manager_instance.update_alarm(
                    climate_alarms['caution'],
                    alarm_message
                )
                print(f"Zaktualizowano alarm ostrożności: powolna reakcja systemu klimatyzacji (różnica: {temp_diff:.1f}°C)")

            # Aktualizacja właściwości alarmowych
            self.update_alarm_properties()
        else:
            # Jeśli system klimatyzacji działa prawidłowo, dezaktywuj wszystkie alarmy
            print(f"System klimatyzacji działa prawidłowo. Zadana: {self.climate_temp:.1f}°C, Aktualna: {self.current_internal_temp:.1f}°C, Różnica: {temp_diff:.1f}°C")

            # Dezaktywacja wszystkich alarmów klimatyzacji
            for level, alarm_id in climate_alarms.items():
                if alarm_id:
                    self.alarm_manager_instance.deactivate_alarm(alarm_id)
                    print(f"Dezaktywowano alarm {level} klimatyzacji - system działa prawidłowo")

            # Aktualizacja właściwości alarmowych
            self.update_alarm_properties()

    def go_to_alarm_screen(self):
        """Przechodzi do ekranu alarmów"""
        if hasattr(self, 'root'):
            self.root.current = 'alarm'

def launch_control_panel():
    """Uruchamia panel kontrolny w osobnym procesie"""
    import subprocess
    import sys
    import threading

    def run_control_panel():
        try:
            # Uruchomienie panelu kontrolnego w osobnym procesie
            # Dzięki implementacji wzorca Singleton w SystemState,
            # panel kontrolny będzie korzystał z tej samej instancji stanu
            subprocess.Popen([sys.executable, "control_panel.py"],
                            cwd=os.path.dirname(os.path.abspath(__file__)))
            print("Panel kontrolny uruchomiony")
        except Exception as e:
            print(f"Błąd uruchamiania panelu kontrolnego: {e}")

    # Uruchomienie w osobnym wątku, aby nie blokować głównej aplikacji
    thread = threading.Thread(target=run_control_panel)
    thread.daemon = True  # Wątek zostanie zakończony, gdy główna aplikacja się zakończy
    thread.start()

if __name__ == "__main__":
    # Uruchomienie panelu kontrolnego
    launch_control_panel()

    # Uruchomienie głównej aplikacji
    MainApp().run()
