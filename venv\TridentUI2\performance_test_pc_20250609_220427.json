{"system_info": {"platform": "PC/Other", "cpu_count": 28, "python_version": "3.13.3 (tags/v3.13.3:6280bb5, Apr  8 2025, 14:47:33) [MSC v.1943 64 bit (AMD64)]", "optimizations_available": true, "memory_total_mb": 16146, "memory_available_mb": 3362, "memory_percent": 79.2}, "memory_performance": {"memory_alloc_time": 0.00019121170043945312, "gc_time": 0.0032122135162353516, "gc_collected": 0, "cleanup_time": 0.0018382072448730469, "objects_created": 1000}, "io_performance": {"write_time_10ops": 0.006661415100097656, "read_time_10ops": 7.104873657226562e-05, "avg_write_time": 0.0006661415100097656, "avg_read_time": 7.104873657226563e-06, "io_stats": {"reads": 0, "writes": 1, "cache_ttl": 0.2, "write_interval": 0.5}}, "alarm_performance": {"add_20_alarms_time": 0.002513408660888672, "get_alarms_10x_time": 0.00043773651123046875, "acknowledge_10_alarms_time": 0.001878499984741211, "deactivate_20_alarms_time": 0.0036361217498779297, "avg_add_time": 0.0001256704330444336, "avg_get_time": 4.3773651123046874e-05, "avg_ack_time": 0.0001878499984741211, "avg_deactivate_time": 0.00018180608749389647}, "ui_performance": {"updates_tested": 100, "avg_update_time": 6.413459777832031e-07, "max_update_time": 4.291534423828125e-06, "min_update_time": 4.76837158203125e-07, "target_fps": 30, "theoretical_fps": 1559220.8178438663, "fps_achievable": true}, "test_summary": {"total_test_time": 0.027096986770629883, "tests_completed": 5, "timestamp": "2025-06-09T22:04:27.366415", "platform": "other"}}