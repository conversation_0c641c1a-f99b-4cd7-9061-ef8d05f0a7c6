# TridentOS - Instrukcje uruchomienia

## <PERSON><PERSON>b<PERSON> start

### Windows
```bash
# Kliknij dwukrotnie na plik:
start_tridentos_windows.bat

# Lub w terminalu:
cd "venv\TridentUI2"
python main.py
```

### Raspberry Pi 5
```bash
# Instalacja (jednorazowo):
chmod +x install_rpi5.sh
./install_rpi5.sh

# Uruchomienie:
./start_tridentos_rpi5.sh

# Lub bezpośrednio:
cd venv/TridentUI2
python start_rpi5.py
```

## Optymalizacje wprowadzone

### ✅ Automatyczne wykrywanie platformy
- Raspberry Pi 5: Aktywne wszystkie optymalizacje
- PC/Windows: Zachowana pełna funkcjonalność
- Automatyczne dostosowanie parametrów

### ✅ Optymalizacje wydajności
- **UI**: 200ms interwał na RPi5 (zamiast 100ms)
- **Stan**: 1s interwał na RPi5 (zamiast 500ms)
- **Alarmy**: 2s interwał na RPi5 (zamiast 1s)
- **Pamięć**: Czyszczenie co 30s na RPi5

### ✅ Zarządzanie pamięcią
- **Cache obrazów**: 100 na RPi5 (zamiast 200)
- **Cache tekstur**: 300 na RPi5 (zamiast 1000)
- **Garbage Collection**: Bardziej agresywny na RPi5
- **Automatyczne czyszczenie**: Weak references i cache

### ✅ Optymalizacje graficzne
- **FPS**: 25 na RPi5 (zamiast 30)
- **GPU**: Wykorzystanie VideoCore VII
- **VSync**: Włączony na RPi5 dla płynności
- **Tekstury**: Limit 512MB na RPi5
- **Fullscreen**: Automatyczny na RPi5, opcjonalny na PC
- **Sterowanie**: F11 - przełącz fullscreen (PC), ESC - wyjdź z fullscreen (PC)

## Testy wydajności

```bash
# Uruchom test wydajności:
cd venv/TridentUI2
python performance_test.py

# Wyniki zapisywane do pliku JSON
# Przykład: performance_test_pc_20250609_220427.json
```

### Przykładowe wyniki (PC):
- **Pamięć**: Alokacja 1000 obiektów w 0.0002s
- **I/O**: 10 zapisów w 0.0067s, 10 odczytów w 0.0001s
- **Alarmy**: 20 alarmów dodanych w 0.0025s
- **UI**: Teoretyczne 1.5M FPS (bardzo wydajne)

## Struktura plików

```
TridentOS/
├── venv/TridentUI2/           # Główna aplikacja
│   ├── main.py               # Aplikacja główna (zoptymalizowana)
│   ├── start_rpi5.py         # Launcher dla RPi5
│   ├── performance_test.py   # Testy wydajności
│   ├── config/
│   │   └── rpi5_config.py    # Konfiguracja RPi5
│   ├── ui_optimizations.py   # Optymalizacje UI
│   ├── state_manager.py      # Manager stanu (zoptymalizowany)
│   └── alarm_manager.py      # Manager alarmów (zoptymalizowany)
├── install_rpi5.sh           # Instalator dla RPi5
├── start_tridentos_windows.bat # Launcher dla Windows
└── README_RPi5_OPTIMIZATIONS.md # Dokumentacja optymalizacji
```

## Rozwiązywanie problemów

### Błąd: "No module named 'psutil'"
```bash
pip install psutil
```

### Błąd: "No module named 'kivy'"
```bash
pip install kivy
```

### Błąd: "Config not defined"
- Problem rozwiązany w najnowszej wersji
- Sprawdź czy używasz zaktualizowanego main.py

### Niska wydajność na RPi5
1. Sprawdź temperaturę: `vcgencmd measure_temp`
2. Sprawdź throttling: `vcgencmd get_throttled`
3. Uruchom test wydajności: `python performance_test.py`
4. Sprawdź konfigurację GPU w `/boot/config.txt`

### Wysokie użycie pamięci
1. Uruchom test wydajności
2. Sprawdź wyniki w sekcji "memory_performance"
3. Rozważ zmniejszenie limitów cache w `rpi5_config.py`

## Monitoring

### Sprawdzanie stanu systemu (RPi5)
```bash
# Temperatura CPU
vcgencmd measure_temp

# Status throttlingu
vcgencmd get_throttled

# Użycie pamięci
free -h

# Status GPU
vcgencmd get_mem gpu
```

### Logi aplikacji
```bash
# Standardowe logi Kivy
ls ~/.kivy/logs/

# Logi systemowe (jeśli uruchomiono jako usługę)
journalctl -u tridentos -f
```

## Sterowanie aplikacją

### Klawisze skrótów (tylko PC)
- **F11** - Przełącz tryb fullscreen/okno
- **ESC** - Wyjdź z trybu fullscreen

### Tryby wyświetlania
- **Raspberry Pi 5**: Automatyczny fullscreen przy starcie
- **PC/Windows**: Tryb okna z możliwością przełączania na fullscreen

## Konfiguracja zaawansowana

### Dostosowanie parametrów wydajności
Edytuj `venv/TridentUI2/config/rpi5_config.py`:

```python
# Przykład dostosowania dla RPi5
if self.is_rpi:
    return {
        'ui_update_interval': 0.3,      # Wolniejsze UI
        'cache_image_limit': 50,        # Mniej cache
        'gc_interval': 20.0             # Częstsze GC
    }
```

### Wyłączenie fullscreen na RPi5
W `config/rpi5_config.py` zmień:
```python
'fullscreen': False,
'borderless': False,
```

### Wyłączenie optymalizacji
W `main.py` ustaw:
```python
OPTIMIZATIONS_AVAILABLE = False
```

## Wsparcie

### Zgłaszanie problemów
1. Uruchom test wydajności
2. Zapisz wyniki
3. Dołącz logi z `~/.kivy/logs/`
4. Opisz problem i środowisko

### Dalszy rozwój
- Hardware decoding video
- GPU compute
- Multi-threading UI
- Async I/O operations

## Changelog

### v1.0 - Podstawowe optymalizacje
- ✅ Wykrywanie platformy
- ✅ Optymalizacje pamięci
- ✅ Throttling UI

### v1.1 - Zaawansowane optymalizacje  
- ✅ Lazy loading
- ✅ Texture pooling
- ✅ Batch updates
- ✅ Performance monitoring

### v1.2 - Optymalizacje systemowe
- ✅ CPU affinity (RPi5)
- ✅ Automatic installation
- ✅ Performance testing
- ✅ Thermal monitoring

## Licencja

Projekt PoseidonBox/TridentOS z optymalizacjami dla Raspberry Pi 5.
Wszystkie optymalizacje zachowują pełną kompatybilność wsteczną.
