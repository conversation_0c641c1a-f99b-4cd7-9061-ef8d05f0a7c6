"""
Moduł do bezpośredniej obsługi pliku system_state.json.
Zawiera funkcje do odczytu i zapisu danych z/do pliku.
Zoptymalizowany o wydajny mechanizm pamięci podręcznej i minimalizację operacji I/O.
"""

import json
import os
import time
import threading
from datetime import datetime
from functools import lru_cache

# Ścieżka do pliku stanu
# Używamy bezwzględnej <PERSON> do pliku, aby <PERSON><PERSON>, że wszystkie komponenty korzystają z tego samego pliku
STATE_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), "system_state.json")

# Domyślne wartości stanu
DEFAULT_STATE = {
    # Dane klimatyzacji
    "climate_temp": 22,
    "fridge_temp": 4,
    "auto_ac": True,
    "external_temp": 14,
    "fan_power": 50,
    "ac_mode": "off",
    "current_internal_temp": 22,

    # <PERSON> o<PERSON>ia
    "interior_light_active": False,
    "navigation_light_active": False,
    "deck_light_active": False,
    "interior_r": 255,
    "interior_g": 255,
    "interior_b": 255,
    "interior_intensity": 100,

    # Dane baterii
    "battery_level": 80,
    "power_consumption": 15,
    "charging": False,
    "power_source": "Battery",
    "time_remaining": "4h 20m",

    # Parametry symulacji baterii
    "sim_discharge_rate": 0.5,
    "sim_charge_rate": 1.0,

    # Dane zbiorników
    "water_level": 50,
    "fuel_level": 60,

    # Dane silnika i autopilota
    "engine_status": "active",
    "autopilot": False,
    "emergency": False,

    # Znacznik czasu
    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
}

# Wykrywanie platformy dla optymalizacji
import platform
IS_RASPBERRY_PI = platform.machine().startswith('arm') or 'raspberry' in platform.node().lower()

# Pamięć podręczna dla stanu - zoptymalizowana dla RPi5
_state_cache = None
_last_modified_time = 0
_last_cache_update = 0

# Parametry dostosowane do platformy
if IS_RASPBERRY_PI:
    _cache_ttl = 0.5           # Dłuższy TTL na RPi5 dla oszczędności I/O
    _write_interval = 1.0      # Rzadsze zapisy na RPi5
    _max_write_buffer_size = 10 # Mniejszy bufor na RPi5
else:
    _cache_ttl = 0.2           # Krótszy TTL na PC
    _write_interval = 0.5      # Częstsze zapisy na PC
    _max_write_buffer_size = 20 # Większy bufor na PC

_file_lock = threading.RLock()  # Bardziej niezawodny mechanizm blokady dla operacji na pliku

# Licznik operacji I/O dla celów diagnostycznych
_io_read_count = 0
_io_write_count = 0
_io_cache_hits = 0

# Bufor zapisu - przechowuje zmiany do zapisania w jednej operacji
_write_buffer = {}
_write_buffer_count = 0
_last_write_time = 0

@lru_cache(maxsize=1)
def _get_default_state():
    """Zwraca kopię domyślnego stanu z pamięcią podręczną"""
    return DEFAULT_STATE.copy()

def load_state(force_refresh=False):
    """
    Wczytuje stan z pliku system_state.json.
    Wykorzystuje wydajną pamięć podręczną, aby zminimalizować operacje I/O.
    Używa mechanizmu blokady, aby zapobiec konfliktom.

    Args:
        force_refresh (bool): Wymusza odświeżenie pamięci podręcznej

    Returns:
        dict: Słownik z danymi stanu
    """
    global _state_cache, _last_modified_time, _last_cache_update, _io_read_count

    # Użyj blokady dla bezpiecznego dostępu do pamięci podręcznej
    with _file_lock:
        current_time = time.time()

        # Sprawdzenie, czy plik istnieje
        if not os.path.exists(STATE_FILE):
            # Utwórz plik z domyślnymi wartościami
            try:
                with open(STATE_FILE, "w") as f:
                    json.dump(_get_default_state(), f, indent=4)
                _io_write_count += 1
                _state_cache = _get_default_state()
                _last_modified_time = os.path.getmtime(STATE_FILE)
                _last_cache_update = current_time
                return _state_cache.copy()
            except Exception as e:
                # Ciche logowanie błędu bez print
                _state_cache = _get_default_state()
                _last_cache_update = current_time
                return _state_cache.copy()

        # Sprawdzenie czasu modyfikacji pliku tylko jeśli wymuszono odświeżenie
        # lub pamięć podręczna jest nieaktualna
        file_mod_time = 0
        if force_refresh or _state_cache is None or current_time - _last_cache_update >= _cache_ttl:
            try:
                file_mod_time = os.path.getmtime(STATE_FILE)
            except:
                file_mod_time = 0

        # Jeśli pamięć podręczna jest aktualna i nie wymuszono odświeżenia, zwróć jej kopię
        if (_state_cache is not None and
            not force_refresh and
            (file_mod_time == 0 or file_mod_time <= _last_modified_time) and
            current_time - _last_cache_update < _cache_ttl):
            return _state_cache.copy()

        # W przeciwnym razie wczytaj dane z pliku
        try:
            with open(STATE_FILE, "r") as f:
                loaded_data = json.load(f)
                _io_read_count += 1

            # Zachowaj sekcję alarmów jeśli potrzeba
            if 'alarms' not in loaded_data and _state_cache is not None and 'alarms' in _state_cache:
                loaded_data['alarms'] = _state_cache['alarms']

            _state_cache = loaded_data
            _last_modified_time = file_mod_time if file_mod_time > 0 else os.path.getmtime(STATE_FILE)
            _last_cache_update = current_time
            return _state_cache.copy()
        except Exception as e:
            # Ciche logowanie błędu bez print
            if _state_cache is None:
                _state_cache = _get_default_state()
            return _state_cache.copy()

def save_state(state_data):
    """
    Zapisuje stan do pliku system_state.json.
    Wykorzystuje buforowanie zapisu dla lepszej wydajności.
    Aktualizuje również pamięć podręczną.

    Args:
        state_data (dict): Słownik z danymi stanu do zapisania

    Returns:
        bool: True jeśli zapis się powiódł, False w przeciwnym razie
    """
    global _state_cache, _last_modified_time, _last_cache_update, _write_buffer, _last_write_time, _io_write_count

    # Użyj blokady dla bezpiecznego dostępu do pamięci podręcznej i bufora zapisu
    with _file_lock:
        current_time = time.time()

        # Dodanie znacznika czasu
        state_data["timestamp"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # Aktualizacja bufora zapisu
        _write_buffer.update(state_data)

        # Aktualizacja pamięci podręcznej
        if _state_cache is None:
            _state_cache = {}
        _state_cache.update(state_data)

        # Sprawdź, czy nadszedł czas na zapisanie bufora
        if current_time - _last_write_time >= _write_interval:
            try:
                # Sprawdź, czy plik istnieje i wczytaj aktualną zawartość
                current_data = {}
                if os.path.exists(STATE_FILE):
                    try:
                        with open(STATE_FILE, "r") as f:
                            current_data = json.load(f)
                            _io_read_count += 1
                    except Exception:
                        # Jeśli nie można wczytać pliku, użyj aktualnej pamięci podręcznej
                        current_data = _state_cache.copy() if _state_cache else {}

                # Zachowaj sekcję alarmów jeśli potrzeba
                if 'alarms' in current_data and 'alarms' not in _write_buffer:
                    _write_buffer['alarms'] = current_data['alarms']

                # Aktualizuj dane
                current_data.update(_write_buffer)

                # Zapisz do pliku
                with open(STATE_FILE, "w") as f:
                    json.dump(current_data, f, indent=4)
                    _io_write_count += 1

                # Aktualizuj pamięć podręczną
                _state_cache = current_data
                _last_modified_time = os.path.getmtime(STATE_FILE)
                _last_cache_update = current_time

                # Wyczyść bufor zapisu
                _write_buffer = {}
                _last_write_time = current_time

                return True
            except Exception as e:
                # Ciche logowanie błędu bez print
                return False

        return True

# Pamięć podręczna dla aktualnego czasu, odświeżana co sekundę
_current_time_cache = ""
_last_time_update = 0

def get_current_time():
    """
    Zwraca aktualny czas w formacie HH:MM.
    Wykorzystuje pamięć podręczną, aby zredukować wywołania datetime.now().

    Returns:
        str: Aktualny czas w formacie HH:MM
    """
    global _current_time_cache, _last_time_update

    current_time = time.time()

    # Odświeżaj czas tylko co sekundę
    if current_time - _last_time_update >= 1.0:
        _current_time_cache = datetime.now().strftime("%H:%M")
        _last_time_update = current_time

    return _current_time_cache

def get_io_stats():
    """
    Zwraca statystyki operacji I/O dla celów diagnostycznych.

    Returns:
        dict: Słownik ze statystykami operacji I/O
    """
    return {
        "reads": _io_read_count,
        "writes": _io_write_count,
        "cache_ttl": _cache_ttl,
        "write_interval": _write_interval
    }
