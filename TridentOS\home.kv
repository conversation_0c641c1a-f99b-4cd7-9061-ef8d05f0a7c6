<HomeScreen>:
    canvas.before:
        Color:
            rgba: 0.035, 0.065, 0.1, 1
        Rectangle:
            pos: self.pos
            size: self.size

    FloatLayout:
        # HOME title i ikony na górze
        BoxLayout:
            size_hint_y: None
            height: 60
            pos_hint: {"top": 0.98, "right": 0.98}
            padding: [10, 10]
            spacing: 10
            Widget:
                size_hint_x: 0.7
            Label:
                text: "HOME"
                font_size: 40
                color: 0.75, 0.8, 0.85, 1
                pos_hint: {"center_x": 0.5, "center_y": 0.5}
                size_hint_x: 0.2
            Widget:
                size_hint_x: 0.4
            Image:
                source: "icons/wifi.png"
                size_hint: None, None
                size: 32, 32
                allow_stretch: True
            Image:
                source: "icons/settings.png"
                size_hint: None, None
                size: 32, 32
                allow_stretch: True

        # Główny grid 3x3, wyśrodkowany
        GridLayout:
            cols: 3
            rows: 3
            spacing: [30, 25]
            padding: [50, 80, 50, 80]
            size_hint: None, None
            width: 1160
            height: 540
            pos_hint: {"center_x": 0.5, "center_y": 0.5}

            # LIGHTNING
            BoxLayout:
                orientation: 'vertical'
                spacing: 10
                padding: [0, 10, 0, 10]
                canvas.before:
                    Color:
                        rgba: 0.13, 0.25, 0.40, 1
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [20]
                    Color:
                        rgba: 1, 1, 1, 0.13
                    Line:
                        rounded_rectangle: (*self.pos, *self.size, 20)
                        width: 2
                Image:
                    source: "icons/lightning.png"
                    size_hint: None, None
                    size: 70, 70
                    allow_stretch: True
                    pos_hint: {"center_x": 0.5}
                Label:
                    text: "LIGHTNING"
                    font_size: 22
                    color: 0.75, 0.8, 0.85, 1
                    halign: "center"
                    valign: "middle"
                    size_hint_y: None
                    height: 32
                Button:
                    text: "on"
                    size_hint: None, None
                    size: 110, 32
                    pos_hint: {"center_x": 0.5}
                    font_size: 18
                    background_normal: ""
                    background_color: 0.18, 0.38, 0.65, 0.7
                    color: 0.75, 0.8, 0.85, 1
                    border: [12, 12, 12, 12]
                    canvas.before:
                        Color:
                            rgba: 0.18, 0.38, 0.65, 0.7
                        RoundedRectangle:
                            pos: self.pos
                            size: self.size
                            radius: [10]
                    on_release: root.go_to_lightning()

            # CLOCK
            BoxLayout:
                orientation: 'vertical'
                padding: 0
                canvas.before:
                    Color:
                        rgba: 0, 0, 0, 0
                Label:
                    id: clock
                    text: "22:29"
                    font_size: 150
                    color: 1, 1, 1, 1
                    bold: False
                    halign: 'center'
                    valign: 'middle'
                    size_hint_y: None
                    height: 180

            # CLIMATE
            BoxLayout:
                orientation: 'vertical'
                padding: [10, 10, 10, 10]
                canvas.before:
                    Color:
                        rgba: 0.13, 0.25, 0.4, 1
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [20]
                    Color:
                        rgba: 0.75, 0.8, 0.85, 0.2
                    Line:
                        rounded_rectangle: (*self.pos, *self.size, 20)
                        width: 1.5
                BoxLayout:
                    orientation: 'horizontal'
                    size_hint_y: None
                    height: 60
                    Image:
                        source: "icons/fan.png"
                        size_hint: None, None
                        size: 60, 60
                        allow_stretch: True
                        pos_hint: {"center_y": 0.5}
                    Label:
                        text: "CLIMATE"
                        font_size: 24
                        color: 0.75, 0.8, 0.85, 1
                        halign: "left"
                        valign: "middle"
                        size_hint_x: 0.6
                    Label:
                        text: f"{app.climate_temp:.1f}°C"
                        font_size: 30
                        color: 0.75, 0.8, 0.85, 1
                        halign: "right"
                        valign: "middle"
                        size_hint_x: 0.4

            # BATTERY
            BoxLayout:
                orientation: 'vertical'
                spacing: 5
                padding: [0, 10, 0, 10]
                canvas.before:
                    Color:
                        rgba: 0.13, 0.25, 0.40, 1
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [20]
                    Color:
                        rgba: 1, 1, 1, 0.13
                    Line:
                        rounded_rectangle: (*self.pos, *self.size, 20)
                        width: 2
                Image:
                    source: "icons/battery.png"
                    size_hint: None, None
                    size: 70, 70
                    allow_stretch: True
                    pos_hint: {"center_x": 0.5}
                Label:
                    text: "BATTERY"
                    font_size: 22
                    color: 0.75, 0.8, 0.85, 1
                    halign: "center"
                    valign: "middle"
                    size_hint_y: None
                    height: 32
                Label:
                    id: battery_gauge
                    text: "80%"
                    font_size: 16
                    color: 0.75, 0.8, 0.85, 1
                    halign: "center"
                    valign: "bottom"
                    size_hint_y: None
                    height: 20
                ProgressBar:
                    value: 80
                    max: 100
                    height: 26
                    size_hint_y: None
                    background_color: 0.13, 0.18, 0.25, 1
                    color: 0.21, 0.56, 0.86, 1
                    pos_hint: {"center_x": 0.5}
                    canvas.before:
                        Color:
                            rgba: 0.21, 0.56, 0.86, 1
                        RoundedRectangle:
                            pos: self.pos
                            size: self.size
                            radius: [12]

            # EMERGENCY
            BoxLayout:
                orientation: 'vertical'
                spacing: 10
                padding: [0, 10, 0, 10]
                canvas.before:
                    Color:
                        rgba: 0.13, 0.25, 0.40, 1
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [20]
                    Color:
                        rgba: 1, 1, 1, 0.13
                    Line:
                        rounded_rectangle: (*self.pos, *self.size, 20)
                        width: 2
                Image:
                    source: "icons/emergency.png"
                    size_hint: None, None
                    size: 70, 70
                    allow_stretch: True
                    pos_hint: {"center_x": 0.5}
                Label:
                    text: "EMERGENCY"
                    font_size: 22
                    color: 0.75, 0.8, 0.85, 1
                    halign: "center"
                    valign: "middle"
                    size_hint_y: None
                    height: 32

            # WATER
            BoxLayout:
                orientation: 'vertical'
                spacing: 5
                padding: [0, 10, 0, 10]
                canvas.before:
                    Color:
                        rgba: 0.13, 0.25, 0.40, 1
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [20]
                    Color:
                        rgba: 1, 1, 1, 0.13
                    Line:
                        rounded_rectangle: (*self.pos, *self.size, 20)
                        width: 2
                Image:
                    source: "icons/water.png"
                    size_hint: None, None
                    size: 70, 70
                    allow_stretch: True
                    pos_hint: {"center_x": 0.5}
                Label:
                    text: "WATER"
                    font_size: 22
                    color: 0.75, 0.8, 0.85, 1
                    halign: "center"
                    valign: "middle"
                    size_hint_y: None
                    height: 32
                Label:
                    id: water_gauge
                    text: "50%"
                    font_size: 16
                    color: 0.75, 0.8, 0.85, 1
                    halign: "center"
                    valign: "bottom"
                    size_hint_y: None
                    height: 20
                ProgressBar:
                    value: 50
                    max: 100
                    height: 26
                    size_hint_y: None
                    background_color: 0.13, 0.18, 0.25, 1
                    color: 0.21, 0.56, 0.86, 1
                    pos_hint: {"center_x": 0.5}
                    canvas.before:
                        Color:
                            rgba: 0.21, 0.56, 0.86, 1
                        RoundedRectangle:
                            pos: self.pos
                            size: self.size
                            radius: [12]

            # AUTOPILOT
            BoxLayout:
                orientation: 'vertical'
                spacing: 10
                padding: [0, 10, 0, 10]
                canvas.before:
                    Color:
                        rgba: 0.13, 0.25, 0.40, 1
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [20]
                    Color:
                        rgba: 1, 1, 1, 0.13
                    Line:
                        rounded_rectangle: (*self.pos, *self.size, 20)
                        width: 2
                Image:
                    source: "icons/autopilot.png"
                    size_hint: None, None
                    size: 70, 70
                    allow_stretch: True
                    pos_hint: {"center_x": 0.5}
                Label:
                    text: "AUTOPILOT"
                    font_size: 22
                    color: 0.75, 0.8, 0.85, 1
                    halign: "center"
                    valign: "middle"
                    size_hint_y: None
                    height: 32
                Button:
                    id: autopilot_status
                    text: "off"
                    size_hint: None, None
                    size: 110, 32
                    pos_hint: {"center_x": 0.5}
                    font_size: 18
                    background_normal: ""
                    background_color: 0.18, 0.38, 0.65, 0.7
                    color: 0.75, 0.8, 0.85, 1
                    border: [12, 12, 12, 12]
                    canvas.before:
                        Color:
                            rgba: 0.18, 0.38, 0.65, 0.7
                        RoundedRectangle:
                            pos: self.pos
                            size: self.size
                            radius: [10]
                    on_release: root.toggle_autopilot()

            # ENGINE
            BoxLayout:
                orientation: 'vertical'
                spacing: 10
                padding: [0, 10, 0, 10]
                canvas.before:
                    Color:
                        rgba: 0.13, 0.25, 0.40, 1
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [20]
                    Color:
                        rgba: 1, 1, 1, 0.13
                    Line:
                        rounded_rectangle: (*self.pos, *self.size, 20)
                        width: 2
                Image:
                    source: "icons/engine.png"
                    size_hint: None, None
                    size: 70, 70
                    allow_stretch: True
                    pos_hint: {"center_x": 0.5}
                Label:
                    text: "ENGINE"
                    font_size: 22
                    color: 0.75, 0.8, 0.85, 1
                    halign: "center"
                    valign: "middle"
                    size_hint_y: None
                    height: 32
                Button:
                    id: engine_status
                    text: "inactive"
                    size_hint: None, None
                    size: 110, 32
                    pos_hint: {"center_x": 0.5}
                    font_size: 18
                    background_normal: ""
                    background_color: 0.18, 0.38, 0.65, 0.7
                    color: 0.75, 0.8, 0.85, 1
                    border: [12, 12, 12, 12]
                    canvas.before:
                        Color:
                            rgba: 0.18, 0.38, 0.65, 0.7
                        RoundedRectangle:
                            pos: self.pos
                            size: self.size
                            radius: [10]
                    on_release: root.toggle_engine()

            # FUEL
            BoxLayout:
                orientation: 'vertical'
                spacing: 10
                padding: [0, 10, 0, 10]
                canvas.before:
                    Color:
                        rgba: 0.13, 0.25, 0.40, 1
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [20]
                    Color:
                        rgba: 1, 1, 1, 0.13
                    Line:
                        rounded_rectangle: (*self.pos, *self.size, 20)
                        width: 2
                Image:
                    source: "icons/fuel.png"
                    size_hint: None, None
                    size: 70, 70
                    allow_stretch: True
                    pos_hint: {"center_x": 0.5}
                Label:
                    text: "FUEL"
                    font_size: 22
                    color: 0.75, 0.8, 0.85, 1
                    halign: "center"
                    valign: "middle"
                    size_hint_y: None
                    height: 32
                Label:
                    id: fuel_gauge
                    text: "60%"
                    font_size: 16
                    color: 0.75, 0.8, 0.85, 1
                    size_hint_y: None
                    height: 20
                ProgressBar:
                    value: 60
                    max: 100
                    height: 26
                    size_hint_y: None
                    background_color: 0.13, 0.18, 0.25, 1
                    color: 0.21, 0.56, 0.86, 1
                    pos_hint: {"center_x": 0.5}
                    canvas.before:
                        Color:
                            rgba: 0.21, 0.56, 0.86, 1
                        RoundedRectangle:
                            pos: self.pos
                            size: self.size
                            radius: [13]

        # Pasek dolny z HOME i ikonami
        BoxLayout:
            orientation: 'horizontal'
            size_hint_y: None
            height: 60
            padding: [50, 0, 50, 0]
            spacing: 0
            pos_hint: {"bottom": 1}

            Label:
                text: "HOME"
                font_size: 38
                color: 0.75, 0.8, 0.85, 1
                size_hint_x: 0.8
                halign: 'center'
                valign: 'middle'

            BoxLayout:
                size_hint_x: 0.2
                spacing: 15
                padding: [0, 15, 0, 15]
                Image:
                    source: "icons/wifi.png"
                    size_hint: None, None
                    size: 30, 30
                    allow_stretch: True
                Image:
                    source: "icons/settings.png"
                    size_hint: None, None
                    size: 30, 30
                    allow_stretch: True

